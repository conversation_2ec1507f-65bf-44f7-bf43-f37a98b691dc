import { defineConfig, loadEnv } from 'vite'
import { exclude, include } from './build/optimize'
import { getPluginsList } from './build/plugins'
import { __APP_INFO__, alias, pathResolve, root, wrapperEnv } from './build/utils'

export default defineConfig(async ({ mode }) => {
  const { VITE_COMPRESSION, VITE_PORT, VITE_ROUTER_HISTORY, VITE_APP_VCONSOLE } = wrapperEnv(loadEnv(mode, root))
  const outDir = (VITE_ROUTER_HISTORY.split(',')?.[1] || 'dist').replace(/\//g, '')
  const base = VITE_ROUTER_HISTORY.split(',')?.[1] || ''
  return {
    // 部署的基本路径
    base: `${base}/`,
    // 项目根目录
    root,
    // 插件配置
    plugins: getPluginsList(VITE_COMPRESSION, VITE_APP_VCONSOLE),
    resolve: {
      // 路径别名配置
      alias,
    },
    css: {
      preprocessorOptions: {
        scss: {
          // 全局导入 SCSS 变量
          additionalData: `@use "@/assets/styles/var.scss" as var;`,
        },
      },
      // Vite 6 新特性：启用 CSS 预处理器多线程处理，显著提高性能
      // 对于使用大量 SCSS/LESS 的项目，可将性能提升 40%
      preprocessorMaxWorkers: true,
    },
    optimizeDeps: {
      // 需要预构建的依赖
      include,
      // 不需要预构建的依赖
      exclude,
    },
    // 开发服务器配置
    server: {
      // 是否自动打开浏览器
      open: false,
      // 端口号
      port: VITE_PORT,
      // 监听所有网络接口
      host: '0.0.0.0',
      // 本地跨域代理配置
      proxy: {
        '^/api/.*': {
          target: 'http://127.0.0.1:8068',
          // target: 'http://*************:8989',
          changeOrigin: true, // 允许跨域
          rewrite: path => path.replace(/^\/api/, '/'),
        },
      },
      // Vite 6 特性：文件预热，提前转换和缓存文件
      // 可减少首次页面加载时间，避免转换瀑布问题
      warmup: {
        clientFiles: [
          './index.html',
          './src/{views,components}/**/*',
          './src/main.js',
          './src/App.vue',
          './src/router/index.js',
          './src/store/index.js',
        ],
      },
      // HMR 优化配置
      hmr: {
        overlay: true,
      },
    },
    build: {
      // 输出目录
      outDir,
      // 设置为现代浏览器目标，提高性能和减小体积
      target: 'es2020',
      // 是否生成 sourcemap
      sourcemap: false,
      // CSS 兼容性目标，与 target 保持一致
      cssTarget: 'chrome80',
      // 增加打包警告阈值，避免大型项目产生过多警告
      chunkSizeWarningLimit: 4000,
      // Vite 6 新特性：自定义资源内联策略
      // 通过回调函数对不同类型的资源应用不同的内联规则
      assetsInlineLimit: (filePath, content) => {
        // 小于 4kb 的 SVG 文件总是内联，提高页面加载性能
        if (filePath.endsWith('.svg') && content.length < 4096) {
          return true
        }
        // 字体文件不内联，避免增加主包体积
        if (/\.(?:woff2?|ttf|eot)$/.test(filePath)) {
          return false
        }
        // 其他文件遵循默认行为（小于 4kb 的文件内联）
        return undefined
      },
      rollupOptions: {
        input: {
          // 入口文件配置
          index: pathResolve('index.html', import.meta.url),
        },
        // 静态资源分类打包配置
        output: {
          // JS 文件输出规则
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          // 其他资源输出规则
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
          // 手动分块策略，优化首屏加载速度
          manualChunks: (id) => {
            // Vue 核心库单独打包
            if (id.includes('vue') || id.includes('vue-router')) {
              return 'vue-vendor'
            }
            // 状态管理库单独打包
            if (id.includes('pinia')) {
              return 'pinia-vendor'
            }
            // Vant UI 组件库单独打包
            if (id.includes('vant')) {
              return 'vant-ui'
            }
            // HTTP 客户端单独打包
            if (id.includes('alova')) {
              return 'http-client'
            }
            // 常用工具库单独打包
            if (id.includes('dayjs') || id.includes('localforage') || id.includes('js-base64') || id.includes('secure-ls')) {
              return 'utils'
            }
            // 加密相关库单独打包
            if (id.includes('crypto-js') || id.includes('js-md5') || id.includes('sm-crypto')) {
              return 'crypto'
            }
            // 图标库单独打包
            if (id.includes('@iconify/vue')) {
              return 'icons'
            }
            // workspace 包单独打包
            if (id.includes('@cm/utils') || id.includes('@cm/alova')) {
              return 'workspace-vendor'
            }
            // 其他 node_modules 包打包到 vendor
            if (id.includes('node_modules')) {
              return 'vendor'
            }
          },
        },
      },
    },
    define: {
      // 全局常量替换
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(__APP_INFO__),
    },
  }
})
