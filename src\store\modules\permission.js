import { defineStore } from 'pinia'
import { ref } from 'vue'
import { store } from '@/store'

const usePermissionStore = defineStore('permission', () => {
  // 整体路由生成的菜单（静态、动态）
  const wholeMenus = ref([])

  // 设置整体路由生成的菜单（静态、动态）
  const setWholeMenus = (menus) => {
    wholeMenus.value = menus
  }
  return { setWholeMenus, wholeMenus }
}, {
  persist: {
    key: 'pinia-permission',
    paths: ['wholeMenus'],
  },
})
export default () => usePermissionStore(store)
