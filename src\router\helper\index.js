import { crypto } from '@cm/utils/crypto'

// 定义需要编码的特殊字符正则表达式
const encodeReserveRE = /[!'()*]/g
// 特殊字符编码替换函数
const encodeReserveReplacer = c => `%${c.charCodeAt(0).toString(16)}`
// 逗号编码的正则表达式
const commaRE = /%2C/g

/**
 * URL 编码函数，保留部分特殊字符
 * @param {string} str - 需要编码的字符串
 * @returns {string} 返回编码后的字符串
 */
function encode(str) {
  return encodeURIComponent(str)
    .replace(encodeReserveRE, encodeReserveReplacer)
    .replace(commaRE, ',')
}

/**
 * URL 解码函数
 * @param {string} str - 需要解码的字符串
 * @returns {string} 返回解码后的字符串
 */
const decode = decodeURIComponent

/**
 * 判断字符串是否是 base64 编码
 * @param {string} str - 需要判断的字符串
 * @returns {boolean} 返回是否是 base64 编码
 */
function isBase64(str) {
  // 空字符串不是 base64
  if (str === '' || str.trim() === '') {
    return false
  }
  try {
    // 尝试对字符串进行 base64 解码再编码，比较结果是否相同
    return btoa(atob(str)) === str
  }
  catch (error) {
    void error // 明确忽略错误信息
    // 如果解码失败，说明不是合法的 base64 字符串
    return false
  }
}

/**
 * 序列化对象并加密
 * @param {object} obj - 需要序列化的对象
 * @returns {string} 返回加密后的查询字符串
 */
export function stringifyQuery(obj) {
  const res = obj
    ? Object.keys(obj)
        .map((key) => {
          const val = obj[key]

          // 跳过 undefined 值
          if (val === undefined) {
            return ''
          }

          // 处理 null 值
          if (val === null) {
            return encode(key)
          }

          // 处理数组值
          if (Array.isArray(val)) {
            const result = []
            val.forEach((val2) => {
              if (val2 === undefined) {
                return
              }
              if (val2 === null) {
                result.push(encode(key))
              }
              else {
                result.push(`${encode(key)}=${encode(val2)}`)
              }
            })
            return result.join('&')
          }

          // 处理普通值
          return `${encode(key)}=${encode(val)}`
        })
        .filter(x => x.length > 0)
        .join('&')
    : null

  // 如果有查询参数，添加 ? 前缀并加密
  return res ? `?${crypto.encrypt(res)}` : ''
}

/**
 * 解密并反序列化查询字符串
 * @param {string} query - 加密的查询字符串
 * @returns {object} 返回解析后的对象
 */
export function parseQuery(query) {
  const res = {}

  // 移除查询字符串前的特殊字符
  query = query.trim().replace(/^([?#&])/, '')

  if (!query) {
    return res
  }

  // 判断是否需要解密
  query = isBase64(query) ? crypto.decrypt(query) : query

  // 解析查询字符串
  query.split('&').forEach((param) => {
    const parts = param.replace(/\+/g, ' ').split('=')
    const key = decode(parts.shift())
    const val = parts.length > 0 ? decode(parts.join('=')) : null

    // 处理重复的键，转换为数组
    if (res[key] === undefined) {
      res[key] = val
    }
    else if (Array.isArray(res[key])) {
      res[key].push(val)
    }
    else {
      res[key] = [res[key], val]
    }
  })

  return res
}
