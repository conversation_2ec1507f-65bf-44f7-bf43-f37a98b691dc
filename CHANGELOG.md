## 0.0.1 (2025-07-11)



## [0.0.2](https://github.com/784933671/vite-vue-app/compare/v0.0.1...v0.0.2) (2025-07-11)



## 0.0.1 (2025-07-11)



## 1.0.1-beta.1 (2025-07-11)


### Performance Improvements

* **projects:** 项目优化 ([b3de3f9](https://github.com/784933671/vite-vue-app/commit/b3de3f9dfadcaab093a3a0166b2c54015320a720))



# 1.0.0 (2025-07-11)


### Performance Improvements

* **projects:** 项目优化 ([b3de3f9](https://github.com/784933671/vite-vue-app/commit/b3de3f9dfadcaab093a3a0166b2c54015320a720))



## 0.0.2 (2025-07-11)


### Performance Improvements

* **projects:** 项目优化 ([b3de3f9](https://github.com/784933671/vite-vue-app/commit/b3de3f9dfadcaab093a3a0166b2c54015320a720))



## 0.0.1 (2025-06-25)


### Bug Fixes

* 新增打包选择环境脚本 ([92cf399](https://git.code.tencent.com/t784933671/vite-vue-app/commits/92cf399fca49098c60f8088290db639f454b68aa))
* 新增打包选择环境脚本 ([b6c8f4c](https://git.code.tencent.com/t784933671/vite-vue-app/commits/b6c8f4cc6b91cf923bc3a94646ab38cd32d962e5))
* 新增打包选择环境脚本 ([31d4888](https://git.code.tencent.com/t784933671/vite-vue-app/commits/31d48882f09221f80a8979beb92c086f3a6db920))
* 新增alovajs请求库封装 ([8b3fdf0](https://git.code.tencent.com/t784933671/vite-vue-app/commits/8b3fdf0de3560df7d892831b51ee721b37ed4b0a))
* 新增alovajs请求库封装 ([45e102f](https://git.code.tencent.com/t784933671/vite-vue-app/commits/45e102f5a4bf9e19315293d1d742da3a9e72a0d0))
* 新增alovajs请求库封装 ([a9630b3](https://git.code.tencent.com/t784933671/vite-vue-app/commits/a9630b31dd3d90c47ce25e6c7d02f21852e0d941))
* 优化打包选择环境脚本 ([38d9f01](https://git.code.tencent.com/t784933671/vite-vue-app/commits/38d9f01ce0ceff9685ead6d6df42117e8796f275))
* 自动修复eslint检测的信息错误（自动修复的) ([2814ad7](https://git.code.tencent.com/t784933671/vite-vue-app/commits/2814ad777f6ea7d65e19291804ec5286711baf9a))


### Features

* **projects:** 1 ([16f6cde](https://git.code.tencent.com/t784933671/vite-vue-app/commits/16f6cde5eb378b842e9e2ae0af3ad16ba712f909))
* **projects:** 1 ([c3ec2c6](https://git.code.tencent.com/t784933671/vite-vue-app/commits/c3ec2c68bc179a532c4758b70bc63c04e8d5ac4d))
* **projects:** 删除打印日志 ([ab6ece6](https://git.code.tencent.com/t784933671/vite-vue-app/commits/ab6ece6e9783dbd36b099963ebe2588ec57bee61))
* **projects:** 优化其他 ([00bec15](https://git.code.tencent.com/t784933671/vite-vue-app/commits/00bec15024c26fcae1302613c5b80d8dcd6fd6f5))


### Reverts

* Revert "ci(deps): 新增git提交校验功能" ([ea60054](https://git.code.tencent.com/t784933671/vite-vue-app/commits/ea600546583c5833abf0f6e069243479aada5f17))
* Revert "chore(projects): release v0.0.8" ([4c3f2f6](https://git.code.tencent.com/t784933671/vite-vue-app/commits/4c3f2f65077adf8513724b89d3c09a410c73e32a))
* Revert "flx:项目优化" ([a2c3151](https://git.code.tencent.com/t784933671/vite-vue-app/commits/a2c3151f5e66c1a41210080e56d1d0e5e686b527))



## [0.0.14](https://git.code.tencent.com/t784933671/vite-vue-app/compare/v0.0.13...v0.0.14) (2025-06-25)



## [0.0.13](https://git.code.tencent.com/t784933671/vite-vue-app/compare/v0.0.12...v0.0.13) (2025-06-25)



## [0.0.12](https://git.code.tencent.com/t784933671/vite-vue-app/compare/v0.0.10...v0.0.12) (2025-06-25)



## [0.0.11](https://git.code.tencent.com/t784933671/vite-vue-app/compare/v0.0.10...v0.0.11) (2025-06-25)



## [0.0.10](https://git.code.tencent.com/t784933671/vite-vue-app/compare/v0.0.9...v0.0.10) (2025-06-25)


### Features

* **projects:** 1 ([16f6cde](https://git.code.tencent.com/t784933671/vite-vue-app/commits/16f6cde5eb378b842e9e2ae0af3ad16ba712f909))



## [0.0.9](https://git.code.tencent.com/t784933671/vite-vue-app/compare/v0.0.8...v0.0.9) (2025-06-25)



## 0.0.8 (2025-06-25)


### Bug Fixes

* 新增打包选择环境脚本 ([92cf399](https://git.code.tencent.com/t784933671/vite-vue-app/commits/92cf399fca49098c60f8088290db639f454b68aa))
* 新增打包选择环境脚本 ([b6c8f4c](https://git.code.tencent.com/t784933671/vite-vue-app/commits/b6c8f4cc6b91cf923bc3a94646ab38cd32d962e5))
* 新增打包选择环境脚本 ([31d4888](https://git.code.tencent.com/t784933671/vite-vue-app/commits/31d48882f09221f80a8979beb92c086f3a6db920))
* 新增alovajs请求库封装 ([8b3fdf0](https://git.code.tencent.com/t784933671/vite-vue-app/commits/8b3fdf0de3560df7d892831b51ee721b37ed4b0a))
* 新增alovajs请求库封装 ([45e102f](https://git.code.tencent.com/t784933671/vite-vue-app/commits/45e102f5a4bf9e19315293d1d742da3a9e72a0d0))
* 新增alovajs请求库封装 ([a9630b3](https://git.code.tencent.com/t784933671/vite-vue-app/commits/a9630b31dd3d90c47ce25e6c7d02f21852e0d941))
* 优化打包选择环境脚本 ([38d9f01](https://git.code.tencent.com/t784933671/vite-vue-app/commits/38d9f01ce0ceff9685ead6d6df42117e8796f275))
* 自动修复eslint检测的信息错误（自动修复的) ([2814ad7](https://git.code.tencent.com/t784933671/vite-vue-app/commits/2814ad777f6ea7d65e19291804ec5286711baf9a))


### Features

* **projects:** 1 ([c3ec2c6](https://git.code.tencent.com/t784933671/vite-vue-app/commits/c3ec2c68bc179a532c4758b70bc63c04e8d5ac4d))
* **projects:** 删除打印日志 ([ab6ece6](https://git.code.tencent.com/t784933671/vite-vue-app/commits/ab6ece6e9783dbd36b099963ebe2588ec57bee61))
* **projects:** 优化其他 ([00bec15](https://git.code.tencent.com/t784933671/vite-vue-app/commits/00bec15024c26fcae1302613c5b80d8dcd6fd6f5))


### Reverts

* Revert "ci(deps): 新增git提交校验功能" ([ea60054](https://git.code.tencent.com/t784933671/vite-vue-app/commits/ea600546583c5833abf0f6e069243479aada5f17))
* Revert "chore(projects): release v0.0.8" ([4c3f2f6](https://git.code.tencent.com/t784933671/vite-vue-app/commits/4c3f2f65077adf8513724b89d3c09a410c73e32a))
* Revert "flx:项目优化" ([a2c3151](https://git.code.tencent.com/t784933671/vite-vue-app/commits/a2c3151f5e66c1a41210080e56d1d0e5e686b527))



## [0.0.7](https://git.code.tencent.com/t784933671/vite-vue-app/compare/v0.0.6...v0.0.7) (2025-06-25)



## [0.0.6](https://git.code.tencent.com/t784933671/vite-vue-app/compare/v0.0.5...v0.0.6) (2025-06-25)



