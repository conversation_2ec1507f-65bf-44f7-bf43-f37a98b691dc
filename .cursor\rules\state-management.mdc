---
description: 
globs: 
alwaysApply: false
---
# 状态管理

本项目使用Pinia作为状态管理解决方案，提供了一种更现代化、类型安全的Vue状态管理方式。

## 核心配置

- [src/store/index.js](mdc:src/store/index.js) - Pinia主配置文件
- [src/store/modules/](mdc:src/store/modules) - 存储模块目录

## 状态持久化

项目通过`pinia-plugin-persistedstate`插件实现状态持久化，并使用`secure-ls`进行加密存储：

```js
// 使用自定义密钥加密存储
const ls = new SecureLS({
  encryptionSecret: 'iVWKLK3jXe39X8w6sPDSRfsdGkpNDx6R',
})

// 配置持久化插件
store.use(
  createPersistedState({
    storage: {
      setItem: (key, value) => {
        ls.set(key, value)
      },
      getItem: (key) => {
        return ls.get(key)
      },
    },
    key: id => `cm-pinia-${id}`,
  }),
)
```

## 主要存储模块

- `app` - 应用程序全局设置
- `permission` - 用户权限和菜单
- `user` - 用户信息和认证

## 使用示例

```js
// 在组件中使用store
import useAppStore from '@/store/modules/app'

const appStore = useAppStore()
// 读取状态
console.log(appStore.theme)
// 修改状态
appStore.setTheme('dark')
```

