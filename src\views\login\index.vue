<script setup>
import { loginByUsername } from '@/api/user'

definePage({
  name: 'Login',
  meta: {
    title: '登录',
    navBar: true,
    isAuth: false,
  },
})

const router = useRouter()
const showPassword = ref(false)
const isLoading = ref(false)
const username = ref('admin')
const password = ref('admin')
function onSubmit({ username, password }) {
  // 这里添加登录逻辑
  loginByUsername(username, password).then((res) => {
    void res
    router.push('/')
  })
}
</script>

<template>
  <div class="login-page">
    <div class="login-container">
      <!-- 头部区域 -->
      <div class="login-header">
        <div class="logo-container">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round" />
            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round" />
            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round" />
          </svg>
        </div>
        <h1 class="login-title">
          欢迎回来
        </h1>
        <p class="login-subtitle">
          请登录您的账户以继续
        </p>
      </div>
      <van-form class="login-form" @submit="onSubmit">
        <!-- 用户名输入框 -->
        <div class="input-group">
          <van-field
            v-model="username"
            name="username"
            placeholder="用户名或邮箱"
            class="custom-field"
          >
            <template #left-icon>
              <div class="input-icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
              </div>
            </template>
          </van-field>
        </div>

        <!-- 密码输入框 -->
        <div class="input-group">
          <van-field
            v-model="password"
            :type="showPassword ? 'text' : 'password'"
            name="password"
            placeholder="密码"
            class="custom-field"
          >
            <template #left-icon>
              <div class="input-icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
              </div>
            </template>
            <template #right-icon>
              <button
                type="button"
                class="password-toggle"
                @click="showPassword = !showPassword"
              >
                <svg v-if="showPassword" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.231 7.81663 6.62 6.68" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  <path d="M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  <path d="M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.4811 9.80385 14.1962C9.51900 13.9113 9.29449 13.5717 9.14359 13.1984C8.99269 12.8251 8.91855 12.4247 8.92563 12.0219C8.93271 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4859 9.58525 10.1546 9.88 9.88" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
              </button>
            </template>
          </van-field>
        </div>

        <van-button
          type="primary"
          native-type="submit"
          block
          color="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
          class="login-button"
          :loading="isLoading"
          loading-text="登录中..."
        >
          登录
        </van-button>
      </van-form>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  
  // 背景动画效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    animation: gradientShift 8s ease-in-out infinite alternate;
    z-index: -1;
  }
  
  @keyframes gradientShift {
    0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    100% { background: linear-gradient(135deg, #764ba2 0%, #667eea 100%); }
  }
  
  .login-container {
    width: 690px; // 750px * 0.92 = 690px，保持合适的边距
    max-width: 690px;
    padding: 80px 48px; // 增大内边距，提供更好的视觉空间
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(40px); // 增强毛玻璃效果
    border-radius: 48px; // 增大圆角，更现代化
    box-shadow: 0 40px 80px rgba(0, 0, 0, 0.1); // 增强阴影效果
    animation: slideUp 0.6s ease-out;
    transform-origin: center bottom;
    
    @keyframes slideUp {
      0% {
        opacity: 0;
        transform: translateY(100px) scale(0.9);
      }
      100% {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }
    .login-header {
      text-align: center;
      .logo-container {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 176px; // 88px * 2 = 176px，适配750px基准
        height: 176px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 44px; // 22px * 2 = 44px
        box-shadow: 0 24px 64px rgba(102, 126, 234, 0.3); // 增强阴影
        animation: logoFloat 2s ease-in-out infinite, logoRotate 3s ease-in-out infinite;
        animation-delay: 0.3s;
        animation-fill-mode: both;
        
        @keyframes logoFloat {
          0%, 100% { transform: translateY(0px) scale(1); }
          50% { transform: translateY(-10px) scale(1.05); }
        }
        
        @keyframes logoRotate {
          0%, 100% { transform: rotate(0deg); }
          25% { transform: rotate(5deg); }
          75% { transform: rotate(-5deg); }
        }

        svg {
          width: 88px; // 44px * 2 = 88px
          height: 88px;
          color: white;
          animation: iconPulse 2s ease-in-out infinite;
          
          @keyframes iconPulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
          }
        }
      }
      .login-title {
        font-size: 76px; // 38px * 2 = 76px，适配750px基准
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 24px; // 12px * 2 = 24px
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1.2;
        animation: titleSlideIn 0.8s ease-out;
        animation-delay: 0.5s;
        animation-fill-mode: both;
        
        @keyframes titleSlideIn {
          0% {
            opacity: 0;
            transform: translateY(-30px);
            letter-spacing: -5px;
          }
          100% {
            opacity: 1;
            transform: translateY(0);
            letter-spacing: 0;
          }
        }
      }

      .login-subtitle {
        font-size: 36px; // 18px * 2 = 36px，适配750px基准
        color: #718096;
        font-weight: 400;
        line-height: 1.4;
        margin-bottom: 40px;
        animation: subtitleFadeIn 1s ease-out;
        animation-delay: 0.7s;
        animation-fill-mode: both;
        
        @keyframes subtitleFadeIn {
          0% {
            opacity: 0;
            transform: translateY(20px);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }
      }
    }
    .login-form {
      .input-group {
        margin-bottom: 40px;
        animation: inputSlideIn 0.6s ease-out;
        animation-fill-mode: both;
        
        &:nth-child(1) {
          animation-delay: 0.9s;
        }
        &:nth-child(2) {
          animation-delay: 1.1s;
        }
        
        @keyframes inputSlideIn {
          0% {
            opacity: 0;
            transform: translateX(-50px);
          }
          100% {
            opacity: 1;
            transform: translateX(0);
          }
        }
        :deep(.custom-field) {
          background: #f7fafc;
          border: 4px solid #e2e8f0; // 2px * 2 = 4px，增大边框
          border-radius: 16px; // 16px * 2 = 32px，增大圆角
          padding: 0;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &:focus-within {
            border-color: #667eea;
            background: #ffffff;
            box-shadow: 0 0 0 6px rgba(102, 126, 234, 0.1); // 3px * 2 = 6px
            transform: translateY(-4px) scale(1.02); // 2px * 2 = 4px
            animation: inputFocus 0.3s ease-out;
          }
          
          @keyframes inputFocus {
            0% { transform: translateY(-4px) scale(1); }
            50% { transform: translateY(-6px) scale(1.03); }
            100% { transform: translateY(-4px) scale(1.02); }
          }
          .van-field__control {
            padding: 20px 30px; // 20px * 2, 24px * 2，增大内边距
            font-size: 36px; // 18px * 2 = 36px，适配750px基准
            color: #2d3748;
            font-weight: 500;
            line-height: 1.4;
            border: none;
            background: transparent;
            &::placeholder {
              color: #a0aec0;
              font-weight: 400;
            }
          }

          .van-field__left-icon {
            margin-left: 30px; // 24px * 2 = 48px
            display: flex;
            align-items: center;
          }

          .van-field__right-icon {
            margin-right: 10px; // 24px * 2 = 48px
            display: flex;
            align-items: center;
          }

          .van-field__error-message {
            display: none; // 使用自定义错误样式
          }
        }
        .input-icon {
          color: #a0aec0;
          transition: color 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          svg {
            width: 48px; // 24px * 2 = 48px，适配750px基准
            height: 48px;
            display: block;
          }
        }
      }

      // 密码切换按钮样式 - 750px 设计基准优化
      .password-toggle {
        background: none;
        border: none;
        color: #a0aec0;
        cursor: pointer;
        padding: 16px; // 8px * 2 = 16px
        border-radius: 16px; // 8px * 2 = 16px
        min-width: 88px; // 44px * 2 = 88px，保持触摸目标标准
        min-height: 88px;
        display: flex;
        align-items: center;
        justify-content: center;

        svg {
          width: 48px; // 24px * 2 = 48px
          height: 48px;
        }
      }
    }
    
    .login-button {
      animation: buttonSlideUp 0.6s ease-out;
      animation-delay: 1.3s;
      animation-fill-mode: both;
      transition: all 0.3s ease;
      
      @keyframes buttonSlideUp {
        0% {
          opacity: 0;
          transform: translateY(30px) scale(0.9);
        }
        100% {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
      }
      
      &:active {
        transform: translateY(0) scale(0.98);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
      }
    }
  }
}

</style>
