# =============================================================================
# Docker Compose 配置文件
# 
# 这个文件定义了项目的多容器 Docker 应用，包含：
# 1. 开发环境服务配置
# 2. 生产环境服务配置  
# 3. 数据库和其他依赖服务配置
# 4. 网络和数据卷配置
# 
# Docker Compose 的优势：
# 1. 一键启动整个开发环境
# 2. 服务间网络自动配置
# 3. 环境隔离和一致性保证
# 4. 便于团队协作和部署
# =============================================================================


# -----------------------------------------------------------------------------
# 服务定义
# -----------------------------------------------------------------------------
services:
  # ---------------------------------------------------------------------------
  # 开发环境服务
  # ---------------------------------------------------------------------------
  # Vue 开发服务器，支持热重载
  dev:
    # 构建配置
    build:
      context: .
      dockerfile: Dockerfile.dev
    
    # 容器名称
    container_name: vite-vue-dev
    
    # 端口映射：主机端口:容器端口
    ports:
      - "3000:3000"  # 开发服务器端口
    
    # 环境变量
    environment:
      - NODE_ENV=development
      - VITE_PORT=3000
    
    # 数据卷挂载（实现热重载）
    volumes:
      # 挂载源代码目录，支持实时更新
      - .:/app
      # 排除 node_modules，使用容器内的版本
      - /app/node_modules
      # 排除 .git 目录
      - /app/.git
    
    # 工作目录
    working_dir: /app
    
    # 启动命令
    command: pnpm run dev
    
    # 网络配置
    networks:
      - app-network
    
    # 依赖服务（如果有的话）
    # depends_on:
    #   - api
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ---------------------------------------------------------------------------
  # 生产环境服务
  # ---------------------------------------------------------------------------
  # 生产环境 Nginx 服务
  app:
    # 使用主 Dockerfile 构建
    build:
      context: .
      dockerfile: Dockerfile
    
    # 容器名称
    container_name: vite-vue-app
    
    # 端口映射
    ports:
      - "9977:9977"  # 生产服务器端口
    
    # 环境变量
    environment:
      - NODE_ENV=production
    
    # 网络配置
    networks:
      - app-network
    
    # 重启策略
    restart: unless-stopped
    
    # 依赖服务（如果有后端 API）
    # depends_on:
    #   - api
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9977/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ---------------------------------------------------------------------------
  # 后端 API 服务（示例配置）
  # ---------------------------------------------------------------------------
  # 取消注释以下配置来添加后端服务
  # api:
  #   # 后端服务镜像（根据实际情况修改）
  #   image: your-backend-api:latest
  #   
  #   # 容器名称
  #   container_name: backend-api
  #   
  #   # 端口映射
  #   ports:
  #     - "8383:8383"
  #   
  #   # 环境变量
  #   environment:
  #     - NODE_ENV=production
  #     - DATABASE_URL=********************************/mydb
  #   
  #   # 网络配置
  #   networks:
  #     - app-network
  #   
  #   # 依赖数据库服务
  #   depends_on:
  #     - db
  #   
  #   # 重启策略
  #   restart: unless-stopped

  # ---------------------------------------------------------------------------
  # 数据库服务（示例配置）
  # ---------------------------------------------------------------------------
  # PostgreSQL 数据库服务
  # db:
  #   # 使用官方 PostgreSQL 镜像
  #   image: postgres:15-alpine
  #   
  #   # 容器名称
  #   container_name: postgres-db
  #   
  #   # 环境变量
  #   environment:
  #     POSTGRES_DB: mydb
  #     POSTGRES_USER: user
  #     POSTGRES_PASSWORD: password
  #   
  #   # 端口映射（可选，用于外部访问）
  #   ports:
  #     - "5432:5432"
  #   
  #   # 数据持久化
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   
  #   # 网络配置
  #   networks:
  #     - app-network
  #   
  #   # 重启策略
  #   restart: unless-stopped

  # ---------------------------------------------------------------------------
  # Redis 缓存服务（示例配置）
  # ---------------------------------------------------------------------------
  # redis:
  #   # 使用官方 Redis 镜像
  #   image: redis:7-alpine
  #   
  #   # 容器名称
  #   container_name: redis-cache
  #   
  #   # 端口映射
  #   ports:
  #     - "6379:6379"
  #   
  #   # 网络配置
  #   networks:
  #     - app-network
  #   
  #   # 重启策略
  #   restart: unless-stopped

# -----------------------------------------------------------------------------
# 网络定义
# -----------------------------------------------------------------------------
networks:
  # 应用网络，所有服务都连接到这个网络
  app-network:
    driver: bridge

# -----------------------------------------------------------------------------
# 数据卷定义
# -----------------------------------------------------------------------------
# volumes:
  # PostgreSQL 数据持久化卷
  # postgres_data:
  
  # Node.js 模块缓存卷（可选）
  # node_modules: 