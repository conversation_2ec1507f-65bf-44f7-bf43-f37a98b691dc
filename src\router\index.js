import { setupLayouts } from 'virtual:generated-layouts'
import { createRouter } from 'vue-router'
import { handleHotUpdate, routes } from 'vue-router/auto-routes'
import { fistPage } from '@/config/website'
import { authGuard, initAuthListeners } from './guards/auth'
import { navigationGuard, scrollBehaviorHandler } from './guards/navigation'
import { permissionGuard } from './guards/permission'
import { parseQuery, stringifyQuery } from './helper'
import { getHistoryMode } from './utils'

// 设置布局的路由
const _routes = setupLayouts(routes)

// 环境变量
const { VITE_ROUTER_HISTORY } = import.meta.env

/**
 * 创建路由实例
 */
const router = createRouter({
  history: getHistoryMode(VITE_ROUTER_HISTORY),
  routes: [
    {
      path: '/',
      name: 'layoutIndex',
      redirect: fistPage.path,
      component: () => import('@/layout/index.vue'),
      children: [],
    },
    ..._routes,
  ],
  strict: true,
  stringifyQuery,
  parseQuery,
  scrollBehavior: scrollBehaviorHandler,
})

// 热更新时，更新路由配置
if (import.meta.hot) {
  handleHotUpdate(router)
}

/**
 * 路由守卫组合器
 * 按顺序执行各个守卫，根据守卫返回值决定路由行为
 * @param {Array} guards - 守卫函数数组
 * @returns {Function} 组合后的守卫函数
 */
function composeGuards(guards) {
  return async (to, from, next) => {
    for (const guard of guards) {
      try {
        const result = await guard(to, from)
        // 根据守卫返回值决定路由行为
        if (result === false) {
          // 守卫返回false，阻止路由跳转
          next(false)
          return
        }
        else if (typeof result === 'string') {
          // 守卫返回字符串，重定向到指定路径
          next(result)
          return
        }
        else if (result && typeof result === 'object') {
          // 守卫返回对象，重定向到指定路由
          next(result)
          return
        }
        // 守卫返回true或undefined，继续执行下一个守卫
      }
      catch (error) {
        console.error('路由守卫执行失败:', error)
        // 出现错误时，阻止路由跳转
        next(false)
        return
      }
    }

    // 所有守卫都通过，允许路由跳转
    next()
  }
}

/**
 * 配置路由守卫
 */
function setupRouterGuards() {
  // 定义守卫执行顺序
  const guards = [
    navigationGuard, // 1. 处理导航相关逻辑（页面标题等）
    authGuard, // 2. 认证检查
    permissionGuard, // 3. 权限检查
  ]

  // 应用组合守卫
  router.beforeEach(composeGuards(guards))
}

/**
 * 初始化路由系统
 */
function initRouterSystem() {
  // 设置路由守卫
  setupRouterGuards()

  // 初始化认证相关监听器
  initAuthListeners()
}

// 初始化路由系统
initRouterSystem()

export default router
