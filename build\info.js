import boxen from 'boxen'
import gradient from 'gradient-string'
import { getPackageSize } from './utils'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'

dayjs.extend(duration)
const boxenOptions = {
  padding: 0.5,
  borderColor: 'cyan',
  borderStyle: 'round',
}

export function viteBuildInfo() {
  let config
  let startTime
  let endTime
  let outDir
  return {
    name: 'vite:buildInfo',
    configResolved(resolvedConfig) {
      config = resolvedConfig
      outDir = resolvedConfig.build?.outDir ?? 'dist'
    },
    buildStart() {
      if (config.command === 'build') {
        startTime = dayjs(new Date())
      }
    },
    closeBundle() {
      if (config.command === 'build') {
        endTime = dayjs(new Date())
        getPackageSize({
          folder: outDir,
          callback: (size) => {
            console.log(
              boxen(
                gradient(['cyan', 'magenta']).multiline(
                  `🎉 恭喜${config.env.VITE_NODE_ENV}构建完成（总用时${dayjs
                    .duration(endTime.diff(startTime))
                    .format('mm分ss秒')}，打包后的大小为${size}）`,
                ),
                boxenOptions,
              ),
            )
          },
        })
      }
    },
  }
}
