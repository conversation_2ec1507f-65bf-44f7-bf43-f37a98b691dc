# @cm/utils

通用工具库，提供加密、存储管理、深拷贝和树结构处理功能。

## 功能特性

- **加密工具** - 提供 AES/DES 加密解密功能
- **存储管理** - 安全的本地存储管理器，支持加密、缓存和过期管理
- **深拷贝工具** - 高性能深拷贝，支持循环引用和各种数据类型
- **树结构工具** - 完整的树数据处理解决方案，包含构建、遍历、操作功能

## 安装

```bash
pnpm add @cm/utils
```

## 使用方式

### 加密工具

```js
import { crypto } from '@cm/utils/crypto'

// AES 加密/解密
const encrypted = crypto.encrypt('hello world')
const decrypted = crypto.decrypt(encrypted)

// 自定义密钥加密
const customEncrypted = crypto.encryptAES('data', 'your-key')
const customDecrypted = crypto.decryptAES(customEncrypted, 'your-key')
```

### 存储管理

```js
import storage from '@cm/utils/storage'

// 基本操作
await storage.set('key', 'value')
const value = await storage.get('key')
await storage.remove('key')

// 带过期时间
await storage.set('key', 'value', 3600) // 1小时后过期

// 批量操作
await storage.setBatch({ key1: 'value1', key2: 'value2' })
const values = await storage.getBatch(['key1', 'key2'])
```

### 深拷贝工具

```js
import { cloneDeep, shallowClone, isDeepEqual } from '@cm/utils/deep-clone'

// 深拷贝
const original = { a: 1, b: { c: 2 } }
const cloned = cloneDeep(original)

// 浅拷贝
const shallowCloned = shallowClone(original)

// 深度比较
const isEqual = isDeepEqual(original, cloned) // true
```

### 树结构处理

```js
import {
  buildTreeFromFlat,
  traverseDepthFirst,
  findNode
} from '@cm/utils/tree-utils'

// 从扁平数据构建树
const flatData = [
  { id: 1, name: '根节点', parentId: null },
  { id: 2, name: '子节点', parentId: 1 }
]
const tree = buildTreeFromFlat(flatData)

// 遍历树
traverseDepthFirst(tree, (node, level) => {
  console.log(`Level ${level}: ${node.name}`)
})

// 查找节点
const node = findNode(tree, node => node.id === 2)
```

## API 文档

详细的 API 文档请参考各模块的 JSDoc 注释。
