import { execCommand } from '../shared/index.js'

/**
 * 生成变更日志
 * 使用 conventional-changelog-cli 生成，支持所有 Git 托管平台
 * @param {object} [options={}] - 配置选项
 * @param {boolean} [total=false] - 是否生成完整的变更日志
 * @returns {Promise<void>} Promise that resolves when changelog generation is complete
 * @throws {Error} If changelog generation fails
 */
export async function genChangelog(options = {}, total = false) {
  console.log('📝 正在生成变更日志...')

  try {
    const baseArgs = [
      'conventional-changelog',
      '-p', 'angular', // 使用 Angular 提交规范
      '-i', 'CHANGELOG.md',
      '-s', // 同时输出到文件和控制台
    ]

    // 如果是生成完整变更日志，添加 -r 0 参数
    const args = total
      ? [...baseArgs, '-r', '0']
      : baseArgs

    console.log(total ? '📚 生成完整变更日志...' : '📄 生成增量变更日志...')

    await execCommand('npx', args, { stdio: 'inherit' })
    console.log('✅ 变更日志生成完成！')
  } catch (error) {
    console.error('❌ 变更日志生成失败:', error.message)
    throw error
  }
}
