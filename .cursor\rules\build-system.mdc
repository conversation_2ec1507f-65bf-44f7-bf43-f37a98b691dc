---
description: 
globs: 
alwaysApply: false
---
# 构建系统

本项目使用Vite作为构建工具，提供了快速的开发服务器和优化的生产构建。

## 核心配置文件

- [vite.config.js](mdc:vite.config.js) - Vite主配置文件
- [build/](mdc:build) - 构建相关脚本和工具
- [postcss.config.js](mdc:postcss.config.js) - PostCSS配置
- [tailwindcss.config.js](mdc:tailwindcss.config.js) - TailwindCSS配置

## 主要特性

### 路径别名

使用`@`作为`src/`目录的别名，简化导入路径：

```js
resolve: {
  alias: {
    '@': '/src'
  }
}
```

### CSS预处理

支持SCSS预处理器，并全局导入变量：

```js
css: {
  preprocessorOptions: {
    scss: {
      additionalData: `@use "@/assets/styles/var.scss" as var;`,
    },
  },
  preprocessorMaxWorkers: true,
}
```

### 构建优化

- 代码分割策略，优化首屏加载
- 资源内联策略，减少HTTP请求
- 构建产物分类存放
- 自动导入组件和API

### 开发服务器

```js
server: {
  open: false,
  port: VITE_PORT,
  host: '0.0.0.0',
  proxy: {
    '^/api/.*': {
      target: 'http://127.0.0.1:8383',
      changeOrigin: true,
      rewrite: path => path.replace(/^\/api/, '/'),
    },
  },
  warmup: {
    clientFiles: ['./index.html', './src/{views,components}/*'],
  },
}
```

## 环境变量

项目支持多环境配置，通过`import.meta.env`访问环境变量：

- `VITE_ROUTER_HISTORY` - 路由模式
- `VITE_TITLE` - 应用标题
- `VITE_LOCAL_MENU` - 是否使用本地菜单
- `VITE_COMPRESSION` - 是否启用压缩
- `VITE_PORT` - 开发服务器端口
- `VITE_APP_VCONSOLE` - 是否启用VConsole调试工具

