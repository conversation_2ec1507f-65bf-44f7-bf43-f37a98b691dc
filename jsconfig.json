{
  "compilerOptions": {
    "target": "ESNext", // 指定 ECMAScript 目标版本，ESNext 表示最新版本
    "module": "ESNext", // 指定生成的模块代码，ESNext 表示最新的模块系统
    "moduleResolution": "bundler", // 模块解析策略，bundler 适用于 Vite/Webpack 等打包工具
    "strict": false, // 不启用所有严格类型检查选项
    "jsx": "preserve", // 保留 JSX 以便后续转换，适用于 Vue 项目
    "importHelpers": true, // 从 tslib 导入辅助工具函数
    "experimentalDecorators": true, // 启用实验性的 ES 装饰器
    "strictFunctionTypes": false, // 不对函数参数进行严格检查
    "skipLibCheck": true, // 跳过声明文件的类型检查，提高编译速度
    "esModuleInterop": true, // 启用 CommonJS 和 ES 模块之间的互操作性
    "isolatedModules": true, // 将每个文件作为单独的模块处理
    "allowSyntheticDefaultImports": true, // 允许从没有默认导出的模块中导入默认值
    "forceConsistentCasingInFileNames": true, // 强制文件名大小写一致性
    "sourceMap": true, // 生成相应的 .map 文件，便于调试
    "baseUrl": ".", // 解析非相对模块名的基准目录
    "resolveJsonModule": true, // 允许导入 .json 文件
    "lib": [
      // 指定要包含在编译中的库文件
      "esnext", // 包含所有最新的 ES 特性
      "dom", // 包含 DOM 定义
      "dom.iterable", // 包含可迭代的 DOM 定义
      "scripthost" // 包含 Windows Script Host 系统对象
    ],
    "paths": {
      // 模块名到基于 baseUrl 的路径映射列表
      "@/*": ["src/*"], // @ 符号映射到 src 目录
      "@build/*": ["build/*"] // @build 符号映射到 build 目录
    }
  }
}
