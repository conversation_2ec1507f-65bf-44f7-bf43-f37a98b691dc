<script setup>
import { useRouter } from 'vue-router'

definePage({
  name: '403',
  meta: {
    title: '403',
    isAuth: false,
  },
})
const router = useRouter()
function goHome() {
  router.push('/')
}
</script>

<template>
  <div class="scene bg-[#f8fafc] dark:bg-dark">
    <div class="error-content">
      <div class="number-container">
        <div class="number four">
          4
        </div>
        <div class="lock-container">
          <div class="lock">
            <div class="lock-body">
              <div class="keyhole">
                <div class="keyhole-shine" />
                <div class="keyhole-shine-sweep" />
              </div>
              <div class="lock-shine" />
            </div>
            <div class="lock-hook">
              <div class="lock-hook-body" />
            </div>
            <div class="key">
              <div class="key-body" />
              <div class="key-head" />
              <div class="key-teeth" />
            </div>
          </div>
        </div>
        <div class="number three">
          3
        </div>
      </div>
      <h1 class="title text-[#64748b] dark:text-white">
        访问受限
      </h1>
      <p class="message text-[#64748b] dark:text-white">
        抱歉，您没有权限访问该页面
      </p>
      <button class="home-button" @click="goHome">
        返回首页
      </button>
    </div>
  </div>
</template>

<style scoped>
.scene {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1000px;
  overflow: hidden;
  padding: 32px;
}

.error-content {
  transform-style: preserve-3d;
  padding: 64px;
}

.number-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 32px;
  margin-bottom: 64px;
  transform-style: preserve-3d;
}

.number {
  font-size: 256px;
  font-weight: 800;
  color: #334155;
  text-shadow:
    2px 2px 0 #94a3b8,
    4px 4px 0 #64748b;
  transform: translateZ(50px);
  animation: float 6s ease-in-out infinite;
}

.number.three {
  animation-delay: -3s;
}

.lock-container {
  transform-style: preserve-3d;
  transform: translateZ(80px);
  animation: float 6s ease-in-out infinite;
  animation-delay: -1.5s;
}

.lock {
  position: relative;
  width: 120px;
  height: 120px;
  transform-style: preserve-3d;
}

.lock-body {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 60px;
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  border-radius: 10px;
  box-shadow:
    0 4px 6px rgba(59, 130, 246, 0.3),
    0 10px 20px rgba(37, 99, 235, 0.2);
  animation: pulse 3s ease-in-out infinite;
}

.lock-shine {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, transparent 50%);
  border-radius: 10px;
}

.keyhole {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: #1e40af;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.keyhole-shine {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
  animation: keyhole-glow 2s ease-in-out infinite;
}

.keyhole-shine-sweep {
  position: absolute;
  width: 150%;
  height: 150%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: translateX(-200%) rotate(45deg);
  animation: keyhole-sweep 3s ease-in-out infinite;
}

.lock-hook {
  position: absolute;
  top: -5px;
  left: 50%;
  transform-origin: bottom;
  animation: hook-swing 3s ease-in-out infinite;
}

.lock-hook-body {
  width: 60px;
  height: 50px;
  border: 12px solid #60a5fa;
  border-radius: 30px 30px 0 0;
  transform: translateX(-50%);
  box-shadow:
    0 4px 6px rgba(59, 130, 246, 0.2),
    inset 0 2px 4px rgba(37, 99, 235, 0.2);
}

.key {
  position: absolute;
  top: 50%;
  right: -50px;
  transform-origin: 0 50%;
  animation: key-move 3s ease-in-out infinite;
}

.key-body {
  width: 40px;
  height: 8px;
  background: linear-gradient(90deg, #60a5fa 0%, #93c5fd 100%);
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.key-head {
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background: linear-gradient(45deg, #60a5fa 0%, #93c5fd 100%);
  border-radius: 50%;
}

.key-teeth {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 16px;
  background: linear-gradient(90deg, #60a5fa 0%, #93c5fd 100%);
  border-radius: 2px;
}

.title {
  font-size: 80px;
  margin-bottom: 32px;
  font-weight: 700;
  transform: translateZ(30px);
}

.message {
  font-size: 35px;
  margin-bottom: 64px;
  transform: translateZ(20px);
}

.home-button {
  display: inline-flex;
  align-items: center;
  padding: 16px 64px;
  font-size: 35px;
  color: white;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.home-button:hover {
  transform: translateZ(40px) translateY(-2px);
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.4);
}

@keyframes float {
  0%,
  100% {
    transform: translateZ(50px) translateY(0);
  }
  50% {
    transform: translateZ(50px) translateY(-20px);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: translateX(-50%) scale(1);
  }
  50% {
    transform: translateX(-50%) scale(1.05);
  }
}

@keyframes keyhole-glow {
  0%,
  100% {
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
}

@keyframes keyhole-sweep {
  0% {
    transform: translateX(-200%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) rotate(45deg);
  }
  100% {
    transform: translateX(-200%) rotate(45deg);
  }
}

@keyframes hook-swing {
  0%,
  100% {
    transform: translateX(-50%) rotate(-5deg);
  }
  50% {
    transform: translateX(-50%) rotate(5deg);
  }
}

@keyframes key-move {
  0%,
  100% {
    transform: translateY(-50%) rotate(0) translateX(0);
  }
  50% {
    transform: translateY(-50%) rotate(-15deg) translateX(-5px);
  }
}

@media (max-width: 640px) {
  .number {
    font-size: 192px;
  }

  .lock {
    width: 90px;
    height: 90px;
  }

  .lock-body {
    width: 60px;
    height: 45px;
  }

  .lock-hook-body {
    width: 45px;
    height: 37.5px;
    border-width: 9px;
  }

  .title {
    font-size: 64px;
  }

  .message {
    font-size: 32px;
  }
}
</style>
