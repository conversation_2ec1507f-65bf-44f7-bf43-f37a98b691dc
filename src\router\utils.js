import { cloneDeep } from '@cm/utils/deep-clone'
import { buildHierarchyTree } from '@cm/utils/tree-utils'
import { createWebHashHistory, createWebHistory } from 'vue-router'
import { getRouters } from '@/api/user'
import usePermissionStoreHook from '@/store/modules/permission'
import router from './index'

// 常量定义
const IFrame = () => import('@/layout/frame.vue')
const modulesRoutes = getModulesRoutes()

/**
 * 判断是否为URL链接
 * @param {string} s - 需要判断的字符串
 * @returns {boolean} - 返回是否为URL
 */
const isURL = s => /^https?:\/\/.*/.test(s)

function getModulesRoutes() {
  if (import.meta.env.VITE_LOCAL_MENU === 'true') {
    return import.meta.glob(['/src/views/**/*.{vue,jsx}', '!/src/views/**/components/**/*.{vue,jsx}'])
  }
  return {}
}

/**
 * 获取路由历史模式
 * @param {string} routerHistory - 历史模式配置，格式为'mode,base'
 * @returns {Function} - 返回对应的history对象
 */
function getHistoryMode(routerHistory) {
  const [mode, base = ''] = routerHistory.split(',')

  if (mode === 'hash') {
    return createWebHashHistory(base)
  }
  else if (mode === 'h5') {
    return createWebHistory(base)
  }

  return createWebHashHistory('') // 默认返回hash模式
}

/**
 * 添加通配符路由，处理404页面
 */
function addPathMatch() {
  if (!router.hasRoute('pathMatch')) {
    router.addRoute({
      path: '/:pathMatch(.*)',
      name: 'pathMatch',
      redirect: '/error/404',
    })
  }
}

/**
 * 初始化路由，从后端获取路由配置
 * @returns {Promise} - 返回路由数据的Promise
 */
function initRouter() {
  return new Promise((resolve, reject) => {
    getRouters().then(({ data }) => {
      handleAsyncRoutes(cloneDeep(data))
      resolve(data)
    }).catch((error) => {
      console.error(error)
      reject(new Error('请求失败'))
    })
  })
}

/**
 * 将多级嵌套路由处理成一维数组
 * @param {Array} routesList - 传入路由
 * @returns {Array} - 返回处理后的一维路由
 */
function formatFlatteningRoutes(routesList) {
  if (!routesList?.length) {
    return routesList
  }

  let hierarchyList = buildHierarchyTree(routesList)

  for (let i = 0; i < hierarchyList.length; i++) {
    if (hierarchyList[i].children) {
      hierarchyList = [
        ...hierarchyList.slice(0, i + 1),
        ...hierarchyList[i].children,
        ...hierarchyList.slice(i + 1),
      ]
    }
  }

  return hierarchyList
}

/**
 * 过滤后端传来的动态路由，重新生成规范路由
 * @param {Array} arrRoutes - 后端返回的路由数组
 * @param {object} parent - 父级路由对象
 * @returns {Array} - 返回处理后的路由数组
 */
function addAsyncRoutes(arrRoutes, parent) {
  if (!arrRoutes?.length) {
    return []
  }

  const modulesRoutesKeys = Object.keys(modulesRoutes)

  arrRoutes.forEach((route) => {
    // 处理meta信息
    delete route.alias
    route.meta = {
      keepAlive: route.isOpen === 2,
    }

    const hasChildren = route.children?.length > 0

    // 处理父级路由的redirect和name
    if (hasChildren) {
      if (!route.redirect) {
        route.redirect = route.children[0].path
      }

      if (!route.name) {
        route.name = `${route.children[0].name}Parent`
      }
    }

    // 处理外部链接
    if (isURL(route.path)) {
      route.meta.frameSrc = route.path
      route.path = `${parent.path}/${route.code}`
      route.component = IFrame
    }
    // 处理父级路由
    else if (hasChildren) {
      route.component = undefined
    }
    // 处理叶子节点路由
    else {
      // 对后端传component组件路径和不传做兼容
      const componentPath = route.component || route.path
      const index = modulesRoutesKeys.findIndex(path => path.includes(componentPath))
      route.component = modulesRoutes[modulesRoutesKeys[index]]
    }

    // 递归处理子路由
    if (hasChildren) {
      addAsyncRoutes(route.children, route)
    }
  })

  return arrRoutes
}

/**
 * 处理后端传来的动态路由
 * @param {Array} routeList - 后端传来的动态路由
 */
function handleAsyncRoutes(routeList) {
  if (!routeList?.length) {
    usePermissionStoreHook().handleWholeMenus(routeList)
    addPathMatch()
    return
  }

  const routes = addAsyncRoutes(cloneDeep(routeList))
  const mainRoute = router.options.routes[0]
  formatFlatteningRoutes(routes).forEach((route) => {
    // 防止重复添加路由
    const routeExists = mainRoute.children.some(child => child.path === route.path)
    if (!routeExists) {
      // 添加到主路由的children中
      route.children = undefined
      mainRoute.children.push(route)

      // 添加到路由表中
      if (!router.hasRoute(route?.name)) {
        router.addRoute(route)
      }
      router.addRoute(mainRoute.name, route)
    }
  })

  usePermissionStoreHook().setWholeMenus(routeList)
  addPathMatch()
}

export { formatFlatteningRoutes, getHistoryMode, initRouter }
