import { h, render } from 'vue'

/**
 * 函数式弹窗
 * @param component 组件
 * @param options 组件参数
 */
function createDialog(component, options) {
  return new Promise((resolve, reject) => {
    // 创建一个div节点
    const mountNode = document.createElement('div')
    mountNode.setAttribute('class', 'btn_class')
    // 将div节点拼接到Dom的body节点下
    document.body.appendChild(mountNode)
    // 使用h函数创建节点
    const vNode = h(component, {
      ...options,
      // 注意: vue子组件emit回调事件名称必须以on开头
      onSubmit: (data) => {
        resolve(data)
        // 移除节点
        document.body.removeChild(mountNode)
      },
      onCancel: (data) => {
        reject(data)
        // 移除节点
        document.body.removeChild(mountNode)
      },
    })
    // 渲染Dialog
    render(vNode, mountNode)
  })
}

export default createDialog
