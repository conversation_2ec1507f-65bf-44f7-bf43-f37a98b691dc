# Vite Vue 移动端应用框架

这是一个基于 Vue 3、Vite 和 Vant UI 的移动端应用开发框架。本项目采用 **Monorepo（单体仓库）** 架构，使用 pnpm workspace 进行多包管理。项目使用了 Vue 3 的 Composition API 和 setup 语法糖，结合 Vant UI 组件库，为移动端应用开发提供了完整的解决方案。

## 项目架构

本项目采用 Monorepo 架构，具有以下特点：

- **统一管理**：多个相关包在同一个仓库中统一管理和维护
- **依赖共享**：包之间可以方便地共享依赖和代码
- **版本协调**：确保所有包使用一致的版本和配置
- **高效开发**：支持跨包的代码重构和类型检查

### 包结构

- **主应用**：位于根目录，包含移动端应用的主要功能
- **@cm/alova**：位于 `packages/alova`，提供封装的 HTTP 客户端库

## 功能特点

- **Vue 3 生态系统**：使用 Vue 3、Vue Router 4 和 Pinia 进行开发
- **Vant UI 集成**：集成了 Vant 4.x 版本的 UI 组件库，提供丰富的移动端组件
- **Vite 构建工具**：基于 Vite 的快速开发和构建体验
- **响应式布局**：使用 Tailwind CSS 和自定义 SCSS 样式
- **登录认证**：内置完整的登录认证流程和权限控制
- **网络请求**：基于 Alova 的封装，支持请求拦截、响应拦截、缓存管理和 Vue hooks 集成
- **加密工具**：集成多种加密工具，如 crypto-js、js-md5 和 sm-crypto
- **本地存储**：使用 localforage 增强的本地存储方案
- **代码规范**：集成 ESLint 进行代码检查

## 环境要求

- **Node.js**: ^18.18.0 或 ^20.9.0 或 >=21.1.0
- **包管理器**: pnpm >=9 （**必须使用**）

### 为什么选择 pnpm？

在 Monorepo 架构中，pnpm 提供了以下优势：

- **Workspace 支持**：原生支持 workspace 功能，完美适配 Monorepo 架构
- **磁盘效率**：通过硬链接和符号链接大幅减少磁盘占用
- **安全性**：严格的依赖解析，避免幽灵依赖问题
- **性能优化**：并行安装和缓存机制，显著提升安装速度
- **兼容性**：与 npm 和 yarn 生态系统完全兼容

## 安装指南

### 克隆仓库

```bash
git clone [仓库地址]
cd vite-vue-app
```

### 安装 pnpm

如果你还没有安装 pnpm，请先安装：

```bash
# 方式 1：使用 npm 安装
npm install -g pnpm

# 方式 2：使用 Homebrew（macOS）
brew install pnpm

# 方式 3：使用 Scoop（Windows）
scoop install pnpm

# 方式 4：使用安装脚本（推荐）
curl -fsSL https://get.pnpm.io/install.sh | sh -
```

### 安装项目依赖

**⚠️ 重要提示**：由于这是一个 Monorepo 项目，**必须使用 pnpm** 来正确处理 workspace 依赖关系。

```bash
# 安装所有包的依赖（包括根目录和所有子包）
pnpm install

# 或者使用简写形式
pnpm i
```

### 验证安装

安装完成后，你可以验证 workspace 设置：

```bash
# 查看 workspace 包列表
pnpm ls -r

# 查看 workspace 依赖关系
pnpm ls -r --depth=0
```

### 常见问题

**问：为什么不能使用 npm 或 yarn？**

答：本项目严格要求使用 pnpm，使用其他包管理器会导致以下严重问题：

- **workspace 依赖无法正确解析**：npm/yarn 无法正确处理 `workspace:*` 协议
- **依赖版本冲突**：可能安装错误的依赖版本，导致运行时错误
- **构建失败**：缺少 pnpm workspace 的关键功能支持
- **开发体验差**：无法使用项目配置的 pnpm 脚本和工作流

**问：安装时遇到权限错误怎么办？**

答：建议使用包管理器的非全局安装方式，或者配置 pnpm 使用用户目录：

```bash
# 配置 pnpm 全局目录（可选）
pnpm config set global-dir ~/.pnpm
pnpm config set global-bin-dir ~/.pnpm
```

## 使用方法

### 开发环境

启动开发服务器，支持热模块替换（HMR）：

```bash
pnpm dev
```

默认情况下，开发服务器会在 `http://localhost:3000` 启动（具体端口可在 .env 文件中配置）。

### 生产环境构建

构建生产版本：

```bash
pnpm build
```

构建预发布环境版本：

```bash
pnpm stage
```

构建完成后，输出的文件将在 `dist` 目录中。

### 预览生产构建

在本地预览生产构建：

```bash
pnpm preview
```

## Monorepo 包管理

### 依赖安装详解

#### 基础安装命令

在 Monorepo 环境下，pnpm 提供了强大的依赖管理功能：

```bash
# 为特定子包安装生产依赖
pnpm add alova --filter @cm/alova

# 为特定子包安装开发依赖
pnpm add -D typescript --filter @cm/alova

# 为根项目安装依赖
pnpm add vue --filter vite-vue-app
# 或者在根目录直接安装
pnpm add vue

# 为所有子包安装相同依赖
pnpm add lodash -r

# 为所有子包安装开发依赖
pnpm add -D eslint -r

# 移除特定包的依赖
pnpm remove alova --filter @cm/alova

# 移除所有包的依赖
pnpm remove lodash -r
```

### 运行脚本

```bash
# 在所有包中运行脚本
pnpm run build -r

# 在特定包中运行脚本
pnpm run build --filter @cm/alova

# 并行运行脚本（提升性能）
pnpm run test -r --parallel
```

#### 常用技巧

```bash
# 为多个包同时安装依赖
pnpm add alova --filter "@cm/alova" --filter "vite-vue-app"

# 并行安装提升性能
pnpm install -r --parallel

# 清理缓存重新安装
pnpm store prune && pnpm install
```

### 包之间的依赖

子包可以相互依赖，使用 `workspace:` 协议：

```json
{
  "dependencies": {
    "@cm/alova": "workspace:*"
  }
}
```

### 更新依赖

```bash
# 更新所有包的依赖到最新版本
pnpm update --latest -r

# 更新特定包的依赖
pnpm update --latest --filter @cm/alova

# 检查过时的依赖
pnpm outdated -r
```

### 清理和重建

```bash
# 清理所有 node_modules
pnpm clean:install

# 或者手动清理
rm -rf node_modules packages/*/node_modules
pnpm install
```

## 项目结构

```bash
vite-vue-app/
├── packages/            # 子包目录（Monorepo）
│   └── alova/           # HTTP 客户端封装包
│       ├── src/         # alova 包源代码
│       └── package.json # alova 包配置
├── public/              # 静态资源
├── src/                 # 主应用源代码
│   ├── api/             # API 接口定义
│   ├── assets/          # 项目资源（图片、样式等）
│   │   ├── styles/      # 全局样式文件
│   ├── components/      # 公共组件
│   ├── config/          # 全局配置
│   ├── directives/      # 自定义指令
│   ├── eventListeners/  # 事件监听器
│   ├── layout/          # 布局组件
│   ├── plugins/         # 插件
│   ├── router/          # 路由配置
│   │   ├── modules/     # 路由模块
│   ├── store/           # Pinia 状态管理
│   │   ├── modules/     # 状态模块
│   ├── utils/           # 工具函数
│   │   ├── http/        # HTTP 请求工具
│   ├── views/           # 页面视图
│   │   ├── demo/        # 示例页面
│   │   ├── login/       # 登录页面
│   ├── App.vue          # 根组件
│   └── main.js          # 入口文件
├── .env                 # 环境变量配置（开发环境）
├── .env.production      # 生产环境变量配置
├── .env.stage           # 预发布环境变量配置
├── pnpm-workspace.yaml  # pnpm workspace 配置
├── vite.config.js       # Vite 配置
├── postcss.config.js    # PostCSS 配置
├── jsconfig.json        # JavaScript 配置
└── package.json         # 根目录项目配置
```

### Monorepo 开发最佳实践

#### 1. 依赖管理

```bash
# ✅ 推荐：使用 filter 为特定包安装依赖
pnpm add react --filter @cm/alova

# ❌ 避免：直接在子包目录中使用其他包管理器
cd packages/alova && npm install react  # 会破坏 workspace 结构
```

#### 2. 版本管理

- 所有子包应保持版本同步
- 使用 `workspace:*` 引用本地包
- 定期更新所有包的依赖版本

#### 3. 构建顺序

```bash
# 按依赖顺序构建包
pnpm run build --filter "@cm/alova"
pnpm run build --filter "vite-vue-app"
```

#### 4. 开发调试

```bash
# 同时启动所有开发服务
pnpm run dev -r --parallel

# 只启动特定包的开发服务
pnpm run dev --filter "vite-vue-app"
```

### 常见问题排查

**问题：依赖安装失败**

```bash
# 完全重装
rm -rf node_modules packages/*/node_modules pnpm-lock.yaml
pnpm install
```

**问题：找不到本地包**

```bash
# 确保正确安装 workspace 依赖
pnpm add @cm/alova --filter vite-vue-app --workspace
```

**问题：工作区配置错误**

检查 `pnpm-workspace.yaml` 配置：

```yaml
packages:
  - 'packages/*'
```

#### 最佳实践

- 通用依赖使用 `-r` 安装到所有包
- 特定功能依赖使用 `--filter` 安装到对应包
- 开发工具使用 `-D` 标记为开发依赖
- 定期使用 `pnpm outdated` 检查更新

## 主要依赖

- **框架**：Vue 3、Vue Router 4、Pinia 3
- **UI 库**：Vant 4.x
- **HTTP 客户端**：Alova
- **CSS 框架**：Tailwind CSS 4.x
- **构建工具**：Vite 6.x
- **本地存储**：localforage
- **工具库**：dayjs、crypto-js、js-md5、sm-crypto 等

## 开发指南

### 路由配置

路由配置位于 `src/router` 目录，使用 Vue Router 4.x。项目支持动态路由和权限控制。

### 状态管理

使用 Pinia 3.x 进行状态管理，并集成了持久化插件。状态模块位于 `src/store/modules` 目录。

### 事件监听器系统

项目提供了基于发布-订阅模式的事件监听器系统，位于 `src/utils/eventListeners` 目录。这个系统用于处理全局事件，如 Toast 提示、登录过期等。

#### 基本用法

```js
// 导入事件系统工具
import { eventEmitter, toast, triggerLoginExpired } from '@/utils/eventListeners'

// 显示提示消息
toast.message('操作成功')
toast.success('保存成功')
toast.fail('操作失败')

// 显示/隐藏加载提示
toast.loading('正在加载...')
toast.hideLoading()

// 触发登录过期事件
triggerLoginExpired(() => {
  // 重置加载状态
  this.loading = false
})

// 自定义事件
const unsubscribe = eventEmitter.on('CUSTOM:EVENT', (data) => {
  console.log('收到自定义事件', data)
})

// 触发自定义事件
eventEmitter.emit('CUSTOM:EVENT', { message: '自定义数据' })

// 取消事件订阅
unsubscribe()
```

#### 更多信息

关于事件监听器系统的详细用法，请参考 [事件监听器使用文档](src/utils/eventListeners/README.md)。

### UI 组件

项目集成了 Vant 4.x UI 组件库，用于快速构建移动端界面。

### 网络请求

使用优化的 Alova 封装进行网络请求，支持请求共享、智能缓存、错误处理等高级功能。

#### 基本用法

```js
import { httpGet, httpPost, httpPut, httpDelete } from '@cm/alova'

// GET请求（带智能缓存）
const getData = async () => {
  try {
    const response = await httpGet('/api/users', { page: 1, limit: 10 }, {
      cacheStrategy: 'MEDIUM', // 10分钟缓存
      cacheTags: ['users']
    })
    console.log('获取数据成功:', response)
    return response
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

// POST请求（带缓存失效）
const createUser = async (userData) => {
  try {
    const response = await httpPost('/api/users', userData, {
      invalidateCache: ['users'] // 创建成功后清除用户列表缓存
    })
    console.log('创建用户成功:', response)
    return response
  } catch (error) {
    console.error('创建用户失败:', error)
  }
}

// PUT请求（带缓存失效）
const updateUser = async (id, userData) => {
  try {
    const response = await httpPut(`/api/users/${id}`, userData, {
      invalidateCache: ['users', `user-${id}`] // 更新后清除相关缓存
    })
    console.log('更新用户成功:', response)
    return response
  } catch (error) {
    console.error('更新用户失败:', error)
  }
}

// DELETE请求（带缓存失效）
const deleteUser = async (id) => {
  try {
    const response = await httpDelete(`/api/users/${id}`, null, {
      invalidateCache: ['users', `user-${id}`]
    })
    console.log('删除用户成功:', response)
    return response
  } catch (error) {
    console.error('删除用户失败:', error)
  }
}


```

#### Vue Hooks 用法

优化的 Alova 提供了更便捷的 Vue 3 hooks，支持智能缓存和性能监控：

```js
import { useGet, usePost, usePaginationList, useSearch } from '@cm/alova'
import { ref } from 'vue'

export default {
  setup() {
    // 基础 GET 请求（带缓存）
    const {
      loading,
      data,
      error,
      reload
    } = useGet('/api/users', null, {
      cacheStrategy: 'MEDIUM',
      cacheTags: ['users']
    })

    // 分页列表（自动缓存管理）
    const {
      loading: pageLoading,
      data: userList,
      page,
      pageSize,
      total,
      reload: reloadPage
    } = usePaginationList('/api/users', {
      initialPageSize: 20,
      extraParams: { status: 'active' }
    })

    // 搜索功能（带防抖）
    const keyword = ref('')
    const {
      loading: searching,
      data: searchResults
    } = useSearch('/api/users/search', keyword, {
      debounce: 500
    })

    // POST 请求（自动缓存失效）
    const {
      loading: creating,
      send: createUser
    } = usePost('/api/users', null, {
      methodConfig: {
        invalidateCache: ['users']
      },
      onSuccess: () => {
        reloadPage() // 重新加载列表
      }
    })

    return {
      loading,
      data,
      error,
      reload,
      pageLoading,
      userList,
      page,
      pageSize,
      total,
      reloadPage,
      keyword,
      searching,
      searchResults,
      creating,
      createUser
    }
  }
}
```

#### 智能缓存管理

优化的 Alova 提供了多级缓存策略，可以显著提升应用性能：

```js
import { httpGet, invalidateCache, CACHE_STRATEGIES } from '@cm/alova'

// 使用预定义缓存策略
const getUserProfile = () => {
  return httpGet('/api/user/profile', null, {
    cacheStrategy: 'PERSISTENT', // 24小时缓存
    cacheTags: ['user', 'profile']
  })
}

// 获取配置数据（长期缓存）
const getConfig = () => {
  return httpGet('/api/config', null, {
    cacheStrategy: 'LONG', // 1小时缓存
    cacheTags: ['config']
  })
}

// 获取实时数据（无缓存）
const getRealtimeData = () => {
  return httpGet('/api/realtime', null, {
    cacheStrategy: 'NONE'
  })
}

// 手动清除缓存
invalidateCache(['users', 'profile']) // 清除指定标签的缓存
```

#### 请求配置选项

```js
import { httpPost } from '@cm/alova'

// 高级配置选项
const apiRequest = () => {
  return httpPost('/api/data', userData, {
    timeout: 5000,        // 请求超时
    skipLoading: true,    // 跳过全局loading
    cacheFor: 60000,      // 缓存1分钟
    cacheTags: ['user'],  // 缓存标签
    retry: 3,             // 失败重试次数
    headers: {            // 自定义请求头
      'Custom-Header': 'value'
    }
  })
}
```

#### 错误处理和重试

```js
import { useRequest } from 'alova/client'
import { httpGet } from '@cm/alova'

export default {
  setup() {
    const { loading, data, error, send } = useRequest(
      () => httpGet('/api/users'),
      {
        immediate: false,
        // 错误重试配置
        retry: {
          retry: 3,                    // 重试次数
          backoff: {
            delay: 1000,              // 初始延迟
            multiplier: 2,            // 延迟倍数
            endDelay: 10000          // 最大延迟
          }
        },
        // 错误处理回调
        onError: (error) => {
          console.error('请求失败:', error)
        },
        // 成功回调
        onSuccess: (data) => {
          console.log('请求成功:', data)
        }
      }
    )

    return { loading, data, error, send }
  }
}
```

#### Alova 优势

1. **性能优化**：内置智能缓存机制，减少不必要的网络请求
2. **Vue 集成**：深度集成 Vue 3，提供响应式的 hooks
3. **开发体验**：简化的 API 设计，减少样板代码
4. **类型安全**：完整的 TypeScript 支持
5. **功能丰富**：支持重试、缓存、分页、文件下载等高级功能

### 本地存储

使用增强的 localStorage 工具，位于 `src/utils/localforage/index.js`，提供了更便捷的数据存储和读取方法。

#### 基本用法

本项目使用基于 localforage 的增强存储工具，支持加密存储和自动过期机制。

```js
// 导入存储工具
import storage from '@/utilsutils/localforage/index.js'

// 在Vue组件中使用
export default {
  setup() {
    const saveUserInfo = async () => {
      // 存储数据（永不过期）
      await storage.set('userInfo', {
        id: 1001,
        name: '张三',
        role: 'admin'
      })

      // 存储数据（有效期7天）
      await storage.set('authToken', 'eyJhbGciOiJ...', 7 * 24 * 60 * 60)
    }

    const getUserInfo = async () => {
      // 获取数据（有默认值）
      const userInfo = await storage.get('userInfo', { name: '游客' })
      console.log(userInfo)

      // 检查键是否存在
      const hasToken = await storage.has('authToken')
      if (hasToken) {
        const token = await storage.get('authToken')
        console.log('当前令牌:', token)
      }
    }

    const clearUserData = async () => {
      // 删除单个数据
      await storage.remove('authToken')

      // 清空所有数据
      await storage.clear()
    }

    return {
      saveUserInfo,
      getUserInfo,
      clearUserData
    }
  }
}
```

#### 高级用法

```js
import storage from '@/utils/localforage/index.js'
import { onMounted, ref } from 'vue'

export default {
  setup() {
    const appSettings = ref({})

    // 批量操作示例
    const saveBatchSettings = async () => {
      // 批量存储多个键值
      await storage.setBatch({
        'app:theme': 'dark',
        'app:language': 'zh-CN',
        'app:fontSize': 16
      }, 30 * 24 * 60 * 60) // 30天过期

      // 批量获取多个键值
      appSettings.value = await storage.getBatch([
        'app:theme',
        'app:language',
        'app:fontSize'
      ])
    }

    // 遍历存储示例
    const showAllStorageItems = async () => {
      await storage.iterate((value, key, index) => {
        console.log(`[${index}] ${key}:`, value)
      })
    }

    // 获取数据元信息示例
    const checkTokenExpiry = async () => {
      const metadata = await storage.getMetadata('authToken')
      if (metadata) {
        console.log('更新时间:', new Date(metadata.updated))
        console.log('过期时间:', metadata.expires ? new Date(metadata.expires) : '永不过期')
        console.log('剩余有效期(毫秒):', metadata.remainingTime || '永不过期')

        // 根据剩余时间更新UI状态
        if (metadata.remainingTime && metadata.remainingTime < 24 * 60 * 60 * 1000) {
          console.log('提醒：令牌将在24小时内过期')
        }
      }
    }

    // 自动加载用户配置
    const userProfile = ref(null)
    const isLoading = ref(true)

    onMounted(async () => {
      isLoading.value = true
      try {
        userProfile.value = await storage.get('userProfile', {})
        await saveBatchSettings()
      }
      finally {
        isLoading.value = false
      }
    })

    return {
      appSettings,
      userProfile,
      isLoading,
      saveBatchSettings,
      showAllStorageItems,
      checkTokenExpiry
    }
  }
}
```

## 图标使用手册

本项目提供了强大且灵活的图标系统，支持多种图标格式和使用方式。

### 图标类型

#### 1. Iconify在线图标

Iconify提供了超过100,000个来自流行图标集的矢量图标，可以直接在线使用。

##### 基础用法

```html
<IconifyIconOnline icon="openmoji:beaming-face-with-smiling-eyes" width="60px" height="60px" />
<IconifyIconOnline icon="fxemoji:2hearts" width="60px" height="60px" />
<IconifyIconOnline icon="bi:badge-8k-fill" width="60px" height="60px" />
<IconifyIconOnline icon="mingcute:alarm-1-line" width="60px" height="60px" />
<IconifyIconOnline icon="flat-color-icons:dislike" width="60px" height="60px" />
<IconifyIconOnline icon="wpf:shopping-bag" width="60px" height="60px" />
```

`icon` 属性遵循 `集合名:图标名` 的格式。

##### 常用图标集前缀

- `mdi:` - Material Design Icons
- `ep:` - Element Plus Icons
- `ri:` - Remix Icon
- `ion:` - Ionicons
- `fa6-solid:` - Font Awesome 6 Solid
- `carbon:` - Carbon

在[Iconify图标浏览器](https://icon-sets.iconify.design/)中可以浏览和搜索所有可用图标。

#### 2. Iconify离线图标

离线图标适用于内网环境或需要固定图标不受网络影响的场景。

##### 基础用法

1. 先导入离线图标：

```js
// src/components/ReIcon/src/offlineIcon.js
import { addIcon } from "@iconify/vue/dist/offline";
import HomeFilled from "@iconify-icons/ep/home-filled";
// 添加离线图标
addIcon("ep:home-filled", HomeFilled);
```

2. 使用离线图标：

```html
<IconifyIconOffline  icon="ep:home-filled"  style="font-size: 24px; color: #1989fa;"/>
```

#### 3. Iconfont图标

支持使用阿里巴巴Iconfont图标库的图标。

##### 基础用法

1. 在项目中引入Iconfont项目的CSS：

```js
// 在main.js中引入iconfont.css
import '@/assets/styles/iconfont.css';
```

2. 使用Font-class引用方式：

```html
<FontIcon icon="icon-home" style="font-size: 24px; color: #07c160;"/>
```

3. 使用Symbol引用方式：

```html
<FontIcon icon="home" iconType="svg" style="font-size: 24px; color: #07c160;"/>
或者
<FontIcon icon="home" svg style="font-size: 24px; color: #07c160;"/>

样式可以使用class 或 直接书写属性

```

4. 使用Unicode引用方式：

```html
<FontIcon icon="&#xe600;" iconType="uni" style="font-size: 24px; color: #07c160;"/>
```

### 图标高级用法

#### 样式定制

图标组件支持通过style属性进行样式定制：

```html
<IconifyIconOnline icon="mdi:heart" style="font-size: 32px;color: #ee0a24;opacity: 0.8;transition: all 0.3s;"/>
```

常用样式属性：

- `font-size`: 控制图标大小
- `color`: 控制图标颜色
- `opacity`: 控制透明度
- `transform`: 可以旋转、缩放图标
- `filter`: 添加如阴影、模糊等效果
- `animation`: 添加动画效果

#### 与组件结合使用

##### 按钮中使用图标

```html
<van-button type="primary" size="small">
  <IconifyIconOnline icon="mdi:plus" style="margin-right: 4px;"/>
  新建
</van-button>
```

##### 输入框前置图标

```html
<van-field 
  v-model="keyword" 
  placeholder="请输入搜索关键词"
>
  <template #left-icon>
    <IconifyIconOnline icon="mdi:magnify" style="margin-right: 4px;" />
  </template>
</van-field>
```

##### 带徽标的图标

```html
<van-badge :content="5" max="99">
  <IconifyIconOnline icon="mdi:bell"  style="font-size: 28px;" />
</van-badge>
```

#### 渲染函数中使用

在组件的渲染函数中使用图标：

```js
// 导入useRenderIcon钩子
import { useRenderIcon } from '@/components/ReIcon/src/hooks';


// 在模板中使用
<!-- 下面的component、el-input、el-button只是举例用，useRenderIcon不局限于这些，它支持任何可以用函数渲染图标的组件或者hooks -->

<!-- 传svg图标 -->
<component :is="useRenderIcon(dayIcon)" />

<!-- 传iconify图标 -->
<el-input :prefix-icon="useRenderIcon(Check)" />

<!-- 传在线图标 -->
<el-input :prefix-icon="useRenderIcon('ep:camera-filled')" />

<!-- 传自定义的iconfont图标，分font-class、unicode、symbol三种模式，需要注意的是这三种模式都需要在图标名称最前面加上 IF- 以区别它们是iconfont图标 -->
<!-- 默认font-class模式 -->
<component :is="useRenderIcon('IF-team-iconlogo')" />
<!-- unicode模式，需要在图标名称最后空一格然后加上uni字符即可 -->
<component :is="useRenderIcon('IF-&#xe620; uni')" />
<!-- symbol模式，需要在图标名称最后空一格然后加上svg字符即可 -->
<component :is="useRenderIcon('IF-team-iconlogo svg', { width: '20px', height: '20px' })"/>
```

### 常见问题

#### 如何查找图标?

1. 访问 [Iconify图标浏览器](https://icon-sets.iconify.design/) 查找在线图标。
2. 访问 [Iconfont官网](https://www.iconfont.cn/) 查找Iconfont图标。
3. 在图标网站找到图标后，记下图标名称和前缀（集合名）。

#### 未显示图标怎么办?

如果图标未正确显示，请检查：

1. 图标名称是否正确，包括集合前缀
2. 在线环境下网络连接是否正常
3. 离线图标是否已正确添加到离线图标库
4. Iconfont图标的CSS文件是否已正确引入

#### 如何避免图标闪烁?

在线图标可能因为网络加载出现闪烁。解决方法：

1. 对于重要和常用图标，建议使用离线图标
2. 预加载关键图标
3. 添加占位符或加载状态

## 注意事项

- 确保 Node.js 版本符合要求
- 必须使用 pnpm 作为包管理工具，不支持其他包管理器
- 开发前请先阅读相关配置文件，了解项目结构和开发规范
