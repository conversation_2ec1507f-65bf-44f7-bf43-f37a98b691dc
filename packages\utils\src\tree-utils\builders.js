/**
 * 树构建器模块
 * 提供从扁平数据构建树结构的功能
 */

/**
 * 从扁平数据构建树结构
 * @param {Array} data - 扁平数据数组
 * @param {Object} options - 配置选项
 * @param {string} [options.idKey='id'] - ID字段名
 * @param {string} [options.parentKey='parentId'] - 父节点ID字段名
 * @param {string} [options.childrenKey='children'] - 子节点字段名
 * @returns {Array} 树结构数据
 */
export function buildTreeFromFlat(data, options = {}) {
  if (!Array.isArray(data)) {
    console.warn('data must be an array')
    return []
  }

  if (!data || data.length === 0) {
    return []
  }

  const config = {
    idKey: options.idKey || 'id',
    parentKey: options.parentKey || 'parentId',
    childrenKey: options.childrenKey || 'children',
  }

  const childrenMap = {}
  const nodeMap = {}
  const tree = []

  // 第一遍遍历：建立映射关系
  for (const item of data) {
    const parentId = item[config.parentKey]
    const itemId = item[config.idKey]

    // 初始化子节点映射
    if (childrenMap[parentId] == null) {
      childrenMap[parentId] = []
    }

    // 建立节点映射
    nodeMap[itemId] = item
    childrenMap[parentId].push(item)
  }

  // 第二遍遍历：找出根节点
  for (const item of data) {
    const parentId = item[config.parentKey]
    if (nodeMap[parentId] == null) {
      tree.push(item)
    }
  }

  // 第三遍遍历：构建子节点关系
  for (const rootNode of tree) {
    buildChildrenRelation(rootNode)
  }

  /**
   * 递归构建子节点关系
   * @param {Object} node - 当前节点
   */
  function buildChildrenRelation(node) {
    const nodeId = node[config.idKey]
    if (childrenMap[nodeId] && childrenMap[nodeId].length > 0) {
      node[config.childrenKey] = childrenMap[nodeId]
      
      // 递归处理子节点
      for (const child of node[config.childrenKey]) {
        buildChildrenRelation(child)
      }
    }
  }

  return tree
}

/**
 * 构建层级关系树（为节点添加层级信息）
 * @param {Array} tree - 树结构数据
 * @param {Array} [pathList=[]] - 路径列表
 * @param {Object} [options={}] - 配置选项
 * @param {string} [options.childrenKey='children'] - 子节点字段名
 * @returns {Array} 添加层级信息后的树
 */
export function buildHierarchyTree(tree, pathList = [], options = {}) {
  if (!Array.isArray(tree)) {
    console.warn('tree must be an array')
    return []
  }

  if (!tree || tree.length === 0) {
    return []
  }

  const config = {
    childrenKey: options.childrenKey || 'children',
  }

  for (const [index, node] of tree.entries()) {
    // 添加层级信息
    node.id = index
    node.parentId = pathList.length ? pathList[pathList.length - 1] : null
    node.pathList = [...pathList, node.id]
    node.level = pathList.length
    
    // 生成唯一ID
    node.uniqueId = node.pathList.length > 1 
      ? node.pathList.join('-') 
      : String(node.pathList[0])

    // 递归处理子节点
    const hasChildren = node[config.childrenKey] && node[config.childrenKey].length > 0
    if (hasChildren) {
      buildHierarchyTree(node[config.childrenKey], node.pathList, options)
    }
  }

  return tree
}

/**
 * 删除只有单个子节点的children属性，并构建唯一ID
 * @param {Array} tree - 树结构数据
 * @param {Array} [pathList=[]] - 路径列表
 * @param {Object} [options={}] - 配置选项
 * @param {string} [options.childrenKey='children'] - 子节点字段名
 * @returns {Array} 处理后的树
 */
export function optimizeTreeStructure(tree, pathList = [], options = {}) {
  if (!Array.isArray(tree)) {
    console.warn('tree must be an array')
    return []
  }

  if (!tree || tree.length === 0) {
    return []
  }

  const config = {
    childrenKey: options.childrenKey || 'children',
  }

  for (const [index, node] of tree.entries()) {
    // 如果只有一个子节点，删除children属性
    if (node[config.childrenKey] && node[config.childrenKey].length === 1) {
      delete node[config.childrenKey]
    }

    // 添加层级信息
    node.id = index
    node.parentId = pathList.length ? pathList[pathList.length - 1] : null
    node.pathList = [...pathList, node.id]
    
    // 生成唯一ID
    node.uniqueId = node.pathList.length > 1 
      ? node.pathList.join('-') 
      : String(node.pathList[0])

    // 递归处理子节点
    const hasChildren = node[config.childrenKey] && node[config.childrenKey].length > 0
    if (hasChildren) {
      optimizeTreeStructure(node[config.childrenKey], node.pathList, options)
    }
  }

  return tree
}

/**
 * 通用树构建函数（兼容原有API）
 * @param {Array} data - 数据源
 * @param {string} [id='id'] - ID字段名
 * @param {string} [parentId='parentId'] - 父节点字段名
 * @param {string} [children='children'] - 子节点字段名
 * @returns {Array} 树结构数据
 */
export function handleTree(data, id, parentId, children) {
  return buildTreeFromFlat(data, {
    idKey: id || 'id',
    parentKey: parentId || 'parentId',
    childrenKey: children || 'children',
  })
}

// 默认导出主要函数
export default {
  buildTreeFromFlat,
  buildHierarchyTree,
  optimizeTreeStructure,
  handleTree,
}
