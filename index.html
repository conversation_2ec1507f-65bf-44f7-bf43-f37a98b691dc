<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=0,viewport-fit=cover" />
    <title>%VITE_TITLE%</title>
    <style>
        /* 高端大气上档次的加载动画样式 */
        @keyframes gradientBG {
            0% {
                background-position: 0% 50%;
            }

            50% {
                background-position: 100% 50%;
            }

            100% {
                background-position: 0% 50%;
            }
        }

        @keyframes float {
            0% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-20px);
            }

            100% {
                transform: translateY(0px);
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
            }

            70% {
                transform: scale(1);
                box-shadow: 0 0 0 15px rgba(255, 255, 255, 0);
            }

            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
            }
        }

        @keyframes rotateCircle {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        @keyframes fadeIn {
            0% {
                opacity: 0;
            }

            100% {
                opacity: 1;
            }
        }

        #app-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: linear-gradient(-45deg, #0f172a, #1e293b, #334155, #1e40af);
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            z-index: 9999;
            transition: opacity 0.8s ease-out;
        }

        .loader-container {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 40px;
            animation: float 6s ease-in-out infinite;
        }

        .loader-circle {
            position: relative;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            box-shadow: 0 25px 45px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            animation: pulse 2s infinite;
        }

        .loader-logo {
            position: relative;
            width: 80px;
            height: 80px;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1;
        }

        .logo-shape {
            position: absolute;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            border-radius: 12px;
            transform: rotate(45deg);
        }

        .orbit {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }

        .orbit:nth-child(1) {
            animation: rotateCircle 8s linear infinite;
        }

        .orbit:nth-child(2) {
            width: 140%;
            height: 140%;
            animation: rotateCircle 12s linear infinite reverse;
        }

        .orbit:nth-child(3) {
            width: 180%;
            height: 180%;
            animation: rotateCircle 20s linear infinite;
        }

        .orbit::before {
            content: '';
            position: absolute;
            top: -6px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #fff;
            box-shadow: 0 0 20px #fff, 0 0 40px #fff, 0 0 60px #fff;
        }

        .orbit:nth-child(2)::before {
            background: #60a5fa;
            box-shadow: 0 0 20px #60a5fa, 0 0 40px #60a5fa;
        }

        .orbit:nth-child(3)::before {
            background: #93c5fd;
            box-shadow: 0 0 20px #93c5fd, 0 0 40px #93c5fd;
        }

        .loading-text {
            margin-top: 35px;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            text-align: center;
            animation: fadeIn 2s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .loading-title {
            color: white;
            font-size: 22px;
            font-weight: 300;
            letter-spacing: 2px;
            margin-bottom: 30px;
            text-transform: uppercase;
            opacity: 0.95;
        }

        .progress-container {
            position: relative;
            width: 250px;
            height: 50px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-end;
        }

        .progress-bar {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .progress-fill {
            height: 100%;
            width: 0%;
            background: linear-gradient(to right, #60a5fa, #93c5fd);
            border-radius: 10px;
            transition: width 0.3s ease;
            box-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
        }

        .progress-percentage {
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 30px;
            font-weight: 200;
            letter-spacing: 1px;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<!-- Vant在 iOS 上无法触发组件的点击反馈效果？
这是因为 iOS Safari 默认不会触发 :active 伪类，解决方法是在 body 标签上添加一个空的 ontouchstart 属性： -->
<body ontouchstart="">
    <div id="app">
        <div id="app-loading">
            <div class="loader-container">
                <div class="loader-circle">
                    <div class="loader-logo">
                        <div class="logo-shape"></div>
                    </div>
                    <div class="orbit"></div>
                    <div class="orbit"></div>
                    <div class="orbit"></div>
                </div>
            </div>
            <div class="loading-text">
                <div class="loading-title">精彩即将呈现</div>
            </div>
            <!-- 高端大气上档次的加载动画 -->
        </div>

        <script type="module" src="/src/main.js"></script>
</body>

</html>