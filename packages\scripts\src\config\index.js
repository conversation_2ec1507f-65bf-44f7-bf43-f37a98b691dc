import process from 'node:process'
import { loadConfig } from 'c12'

/**
 * Default CLI configuration options
 * @type {object}
 */
const defaultOptions = {
  cwd: process.cwd(),
  cleanupDirs: [
    '**/dist',
    '**/package-lock.json',
    '**/yarn.lock',
    '**/pnpm-lock.yaml',
    '**/node_modules',
    '!node_modules/**',
  ],
  ncuCommandArgs: ['--deep', '-u'],
  changelogOptions: {},
  gitCommitVerifyIgnores: [
    /^((Merge pull request)|(Merge (.*?) into (.*?)|(Merge branch (.*?)))(?:\r?\n)*$)/m,
    /^(Merge tag (.*?))(?:\r?\n)*$/m,
    /^(R|r)evert (.*)/,
    /^(amend|fixup|squash)!/,
    /^(Merged (.*?)(in|into) (.*)|Merged PR (.*): (.*))/,
    /^Merge remote-tracking branch(\s*)(.*)/,
    /^Automatic merge(.*)/,
    /^Auto-merged (.*?) into (.*)/,
  ],
}

/**
 * Load CLI options with defaults and overrides
 * @param {object} [overrides] - Override options
 * @param {string} [cwd=process.cwd()] - Current working directory
 * @returns {Promise<object>} Loaded configuration
 */
export async function loadCliOptions(overrides, cwd = process.cwd()) {
  const { config } = await loadConfig({
    name: 'cm-scripts',
    defaults: defaultOptions,
    overrides,
    cwd,
    packageJson: true,
  })

  return config
}
