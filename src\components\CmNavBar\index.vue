<script setup>
import { Icon, NavBar } from 'vant'
import { h, useAttrs, useSlots } from 'vue'
import { useRouter } from 'vue-router'

const slots = useSlots()
const attrs = useAttrs()
const router = useRouter()

function goBack() {
  router.back()
}

// 创建自定义渲染函数
function renderNavBar() {
  // 复制所有属性
  const props = { ...attrs }

  // 准备插槽对象
  const slotObj = {}

  // 复制所有插槽到slotObj
  Object.keys(slots).forEach((slotName) => {
    slotObj[slotName] = slots[slotName]
  })

  // 如果没有left插槽，则使用默认的返回箭头图标
  if (!slots.left) {
    slotObj.left = () => h(Icon, {
      name: 'arrow-left',
      size: '24',
      class: '!text-[#333] dark:!text-white',
    })

    // 设置点击左侧事件处理
    props.onClickLeft = props.onClickLeft || goBack
  }

  return h(NavBar, props, slotObj)
}
</script>

<template>
  <component :is="renderNavBar()" />
</template>
