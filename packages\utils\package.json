{"name": "@cm/utils", "type": "module", "version": "0.0.1", "description": "通用工具库，包含加密、存储管理、深拷贝和树结构处理功能", "author": "", "license": "MIT", "exports": {".": "./src/index.js", "./crypto": "./src/crypto.js", "./storage": "./src/storage.js", "./deep-clone": "./src/deep-clone.js", "./validators": "./src/validators.js", "./dom-utils": "./src/dom-utils.js", "./tree-utils": "./src/tree-utils/index.js", "./tree-utils/builders": "./src/tree-utils/builders.js", "./tree-utils/traversal": "./src/tree-utils/traversal.js", "./tree-utils/manipulation": "./src/tree-utils/manipulation.js"}, "dependencies": {"crypto-js": "^4.2.0", "localforage": "^1.10.0", "mitt": "^3.0.1"}}