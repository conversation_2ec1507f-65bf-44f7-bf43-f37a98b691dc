import dayjs from 'dayjs'
import { fileURLToPath } from 'node:url'
import { dirname, resolve } from 'node:path'
import process from 'node:process'
import { dependencies, devDependencies, engines, name, version } from '../package.json'
import { readdir, stat } from 'node:fs'

/** 启动`node`进程时所在工作目录的绝对路径 */
const root = process.cwd()

/**
 * @description 根据可选的路径片段生成一个新的绝对路径
 * @param dir 路径片段，默认`build`
 * @param metaUrl 模块的完整`url`，如果在`build`目录外调用必传`import.meta.url`
 */
function pathResolve(dir = '.', metaUrl = import.meta.url) {
  // 当前文件目录的绝对路径
  const currentFileDir = dirname(fileURLToPath(metaUrl))
  // build 目录的绝对路径
  const buildDir = resolve(currentFileDir, 'build')
  // 解析的绝对路径
  const resolvedPath = resolve(currentFileDir, dir)
  // 检查解析的绝对路径是否在 build 目录内
  if (resolvedPath.startsWith(buildDir)) {
    // 在 build 目录内，返回当前文件路径
    return fileURLToPath(metaUrl)
  }
  // 不在 build 目录内，返回解析后的绝对路径
  return resolvedPath
}

/** 设置别名 */
const alias = {
  '@': pathResolve('../src'),
  '@build': pathResolve(),
}

/** 平台的名称、版本、运行所需的`node`和`pnpm`版本、依赖、最后构建时间的类型提示 */
const __APP_INFO__ = {
  pkg: { name, version, engines, dependencies, devDependencies },
  lastBuildTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
}

/** 处理环境变量 */
function wrapperEnv(envConf) {
  /** 此处为默认值 */
  const ret = {
    VITE_PORT: 8848,
    VITE_APP_BASE_URL: '',
    VITE_CDN: false,
    VITE_COMPRESSION: 'none',
    VITE_PUBLIC_PATH: './',
    VITE_LOCAL_MENU: true,
    VITE_APP_VCONSOLE: false,
  }
  for (const envName of Object.keys(envConf)) {
    let realName = envConf[envName].replace(/\\n/g, '\n')
    realName = realName === 'true' ? true : realName === 'false' ? false : realName

    if (envName === 'VITE_PORT') {
      realName = Number(realName)
    }
    ret[envName] = realName
    if (typeof realName === 'string') {
      process.env[envName] = realName
    }
    else if (typeof realName === 'object') {
      process.env[envName] = JSON.stringify(realName)
    }
  }
  return ret
}

/**
 * 计算数组中所有数字的总和
 * @param {Array<number>} arr - 要求和的数字数组
 * @returns {number} - 总和结果
 */
function sum(arr) {
  return arr.reduce((prev, curr) => prev + curr, 0)
}

/**
 * 将字节数格式化为人类易读的形式
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数点后位数，默认为2
 * @returns {string} - 格式化后的文件大小，如：1.5 KB, 2.3 MB等
 */
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0)
    return '0 Bytes'

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${Number.parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`
}

const fileListTotal = []

/** 获取指定文件夹中所有文件的总大小 */
function getPackageSize(options) {
  const { folder = 'dist', callback, format = true } = options
  readdir(folder, (err, files) => {
    if (err) { 
      throw err
    }
    let count = 0
    const checkEnd = () => {
      if (++count === files.length) {
        callback(format ? formatBytes(sum(fileListTotal)) : sum(fileListTotal))
      }
    }
    files.forEach((item) => {
      stat(`${folder}/${item}`, async (err, stats) => {
        if (err)
          throw err
        if (stats.isFile()) {
          fileListTotal.push(stats.size)
          checkEnd()
        }
        else if (stats.isDirectory()) {
          getPackageSize({
            folder: `${folder}/${item}/`,
            callback: checkEnd,
          })
        }
      })
    })
    files.length === 0 && callback(0)
  })
}

export { root, pathResolve, alias, __APP_INFO__, wrapperEnv, getPackageSize }
