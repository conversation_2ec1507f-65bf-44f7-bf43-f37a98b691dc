# =============================================================================
# Docker 开发环境配置文件
# 
# 这个 Dockerfile 专门用于开发环境，与生产环境的 Dockerfile 相比：
# 1. 不进行构建操作，直接运行开发服务器
# 2. 支持热重载，代码更改立即生效
# 3. 包含开发工具和调试功能
# 4. 挂载源代码目录，便于实时开发
# =============================================================================

# 使用官方 Node.js 镜像作为基础镜像
# 选择 Alpine Linux 版本：体积小、安全性高
FROM node:alpine

# 设置工作目录
WORKDIR /app

# 安装系统级别的开发工具
# curl：用于健康检查和 API 测试
# git：可能需要用于某些 npm 包的安装
RUN apk add --no-cache \
    curl \
    git

# 启用 Corepack 来管理包管理器
# 确保使用项目指定的 pnpm 版本
RUN corepack enable

# 设置 pnpm 存储目录
# 在开发环境中使用全局存储可以节省空间
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# 复制包管理器配置文件
# 这些文件用于确定依赖版本和工作区配置
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./

# 如果存在 .npmrc 文件，也需要复制
COPY .npmrc* ./

# 复制 packages 目录（monorepo 工作区结构）
COPY packages/ ./packages/

# 安装项目依赖
# 开发环境安装所有依赖，包括 devDependencies
RUN pnpm install --frozen-lockfile

# 暴露开发服务器端口
# 这个端口应该与 package.json 中 dev 脚本使用的端口一致
EXPOSE 3000

# 设置环境变量
ENV NODE_ENV=development

# 创建非特权用户（安全最佳实践）
RUN addgroup -g 1001 -S nodejs \
  && adduser -S developer -u 1001

# 修改 app 目录的所有权
RUN chown -R developer:nodejs /app

# 切换到非特权用户
USER developer

# 添加健康检查
# 检查开发服务器是否正常运行
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:3000 || exit 1

# 启动开发服务器
# 使用 --host 0.0.0.0 确保服务器监听所有网络接口
# 这样 Docker 容器外部才能访问到服务
CMD ["pnpm", "run", "dev", "--host", "0.0.0.0"]

# =============================================================================
# 开发环境特性说明：
# 
# 1. 热重载 (Hot Reload)：
#    - 通过 volume 挂载实现代码实时更新
#    - 无需重新构建镜像即可看到代码变化
# 
# 2. 调试支持：
#    - 保留 source map 和开发工具
#    - 支持浏览器开发者工具
# 
# 3. 快速启动：
#    - 跳过构建步骤，直接运行开发服务器
#    - 依赖已预安装，启动速度快
# 
# 4. 开发工具：
#    - 包含 ESLint、Prettier 等开发工具
#    - 支持 TypeScript 类型检查
# 
# 使用方法：
# docker build -f Dockerfile.dev -t vite-vue-dev .
# docker run -p 3000:3000 -v $(pwd):/app vite-vue-dev
# ============================================================================= 