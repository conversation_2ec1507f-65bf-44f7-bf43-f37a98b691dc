import { Toast } from 'vant'

/**
 * 复制指令 v-copy
 * 使用方法：
 * 1. 基础用法：v-copy="'要复制的文本'"
 * 2. 配置用法：v-copy="{ text: '要复制的文本', success: '复制成功提示', error: '复制失败提示' }"
 */
export const copy = {
  mounted(el, binding) {
    // 指令处理函数
    el.handler = () => {
      // 获取复制内容
      let text = ''
      let successMsg = '复制成功！'
      let errorMsg = '复制失败！'

      // 判断绑定值类型
      if (typeof binding.value === 'string') {
        text = binding.value
      }
      else if (binding.value && typeof binding.value === 'object') {
        text = binding.value.text || ''
        successMsg = binding.value.success || successMsg
        errorMsg = binding.value.error || errorMsg
      }

      // 文本为空时不执行操作
      if (!text) {
        Toast('复制的内容不能为空！')
        return
      }

      // 创建一个textarea元素用于复制操作
      const textarea = document.createElement('textarea')
      textarea.value = text
      textarea.readOnly = 'readonly'

      // 设置样式使其不可见
      textarea.style.position = 'absolute'
      textarea.style.left = '-9999px'

      // 添加到DOM中
      document.body.appendChild(textarea)

      // 选中文本
      textarea.select()
      textarea.setSelectionRange(0, text.length)

      // 执行复制命令
      let success = false
      try {
        success = document.execCommand('copy')
        if (success) {
          Toast.success(successMsg)
        }
        else {
          Toast.fail(errorMsg)
        }
      }
      catch (err) {
        Toast.fail(errorMsg)
        console.error('复制指令执行失败:', err)
      }

      // 移除textarea元素
      document.body.removeChild(textarea)
    }

    // 添加点击事件监听器
    el.addEventListener('click', el.handler)

    // 添加指示样式，鼠标移上去显示为可点击状态
    el.style.cursor = 'pointer'
  },

  // 在元素解绑时移除事件监听器
  unmounted(el) {
    el.removeEventListener('click', el.handler)
    delete el.handler
  },
}
