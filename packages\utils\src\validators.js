/**
 * 验证工具模块
 * 提供常用的数据验证功能
 */

/**
 * 判断值是否为空
 * @param {any} val - 待判断的值
 * @returns {boolean} 如果为空返回 true，否则返回 false
 */
export function isEmpty(val) {
  // 布尔值和数字类型不为空
  if (typeof val === 'boolean' || typeof val === 'number') {
    return false
  }

  // 数组判断
  if (Array.isArray(val)) {
    return val.length === 0
  }

  // 对象判断
  if (val instanceof Object && val !== null) {
    return Object.keys(val).length === 0
  }

  // 字符串和其他类型判断
  if (val === 'null' || val == null || val === 'undefined' || val === undefined || val === '') {
    return true
  }

  return false
}

/**
 * 判断值是否不为空（isEmpty 的反向）
 * @param {any} val - 待判断的值
 * @returns {boolean} 如果不为空返回 true，否则返回 false
 */
export function isNotEmpty(val) {
  return !isEmpty(val)
}

/**
 * 判断输入是否为有效的 JSON 或者 JSON 字符串
 * @param {any} val - 待判断的值
 * @returns {boolean} 如果是对象或可解析的 JSON 字符串，返回 true；否则返回 false
 */
export function isValidJSON(val) {
  // 直接判断是否为对象（排除 null）
  if (val !== null && typeof val === 'object') {
    return true
  }

  // 尝试解析字符串为 JSON
  if (typeof val === 'string') {
    try {
      const obj = JSON.parse(val)
      // 解析后还需判断是否为对象或数组
      return obj !== null && typeof obj === 'object'
    }
    catch (error) {
      void error // 明确忽略错误信息
      return false
    }
  }

  return false
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 格式正确返回 true
 */
export function isValidEmail(email) {
  if (typeof email !== 'string') return false
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证手机号格式（中国大陆）
 * @param {string} phone - 手机号
 * @returns {boolean} 格式正确返回 true
 */
export function isValidPhone(phone) {
  if (typeof phone !== 'string') return false
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证身份证号格式（中国大陆）
 * @param {string} idCard - 身份证号
 * @returns {boolean} 格式正确返回 true
 */
export function isValidIdCard(idCard) {
  if (typeof idCard !== 'string') return false
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard)
}

/**
 * 验证URL格式
 * @param {string} url - URL地址
 * @returns {boolean} 格式正确返回 true
 */
export function isValidURL(url) {
  if (typeof url !== 'string') return false
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 验证IP地址格式
 * @param {string} ip - IP地址
 * @returns {boolean} 格式正确返回 true
 */
export function isValidIP(ip) {
  if (typeof ip !== 'string') return false
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  return ipRegex.test(ip)
}

// 兼容原有API
export const validatenull = isEmpty
export const validatejson = isValidJSON

// 默认导出
export default {
  isEmpty,
  isNotEmpty,
  isValidJSON,
  isValidEmail,
  isValidPhone,
  isValidIdCard,
  isValidURL,
  isValidIP,
  // 兼容性别名
  validatenull: isEmpty,
  validatejson: isValidJSON,
}
