<script setup>
import { Field } from 'vant'
import { getCurrentInstance, h } from 'vue'

defineOptions({
  inheritAttrs: true,
})

const instance = getCurrentInstance()

function changeRef(inputInstance) {
  instance.exposeProxy = instance.exposed = inputInstance || {}
}
</script>

<template>
  <div>
    <component :is="h(Field, { ...$attrs, ref: changeRef() }, $slots)" />
  </div>
</template>

<style lang="scss" scoped></style>
