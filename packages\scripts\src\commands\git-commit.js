import { readFileSync } from 'node:fs'
import { join } from 'node:path'
import process from 'node:process'
import enquirer from 'enquirer'
import { locales } from '../locales/index.js'
import { execCommand } from '../shared/index.js'

const { prompt } = enquirer

/**
 * Git commit with Conventional Commits standard
 * 支持交互式推送确认功能，提供更灵活的工作流程控制
 * @param {string} [lang] - Language setting
 * @returns {Promise<void>} Promise that resolves when commit is complete
 */
export async function gitCommit(lang = 'en-us') {
  try {
    const { gitCommitMessages, gitCommitTypes, gitCommitScopes } = locales[lang]

    const typesChoices = gitCommitTypes.map(([value, msg]) => ({
      name: value,
      message: `${`${value}:`.padEnd(12)}${msg}`,
    }))

    const scopesChoices = gitCommitScopes.map(([value, msg]) => ({
      name: value,
      message: `${value.padEnd(30)} (${msg})`,
    }))

    const { types, scopes, description } = await prompt([
      {
        name: 'types',
        type: 'select',
        message: gitCommitMessages.types,
        choices: typesChoices,
      },
      {
        name: 'scopes',
        type: 'select',
        message: gitCommitMessages.scopes,
        choices: scopesChoices,
      },
      {
        name: 'description',
        type: 'text',
        message: gitCommitMessages.description,
      },
    ])

    const breaking = description.startsWith('!') ? '!' : ''
    const cleanDescription = description.replace(/^!/, '').trim()
    const commitMsg = `${types}(${scopes})${breaking}: ${cleanDescription}`

    // 在提交前自动暂存所有更改
    console.log('📦 正在暂存所有更改...')
    await execCommand('git', ['add', '.'], { stdio: 'inherit' })

    console.log('✅ 正在提交更改...')
    await execCommand('git', ['commit', '-m', commitMsg], { stdio: 'inherit' })

    // 询问用户是否要推送到远程仓库
    const pushConfirm = await prompt({
      name: 'shouldPush',
      type: 'confirm',
      message: '🚀 是否要推送到远程仓库？',
      initial: true,
    })

    if (pushConfirm.shouldPush) {
      try {
        console.log('🚀 正在推送到远程仓库...')
        await execCommand('git', ['push'], { stdio: 'inherit' })
        console.log('✅ 推送完成！')
      }
      catch (error) {
        console.error('❌ 推送失败:', error.message)
        console.log('ℹ️  您可以稍后手动执行: git push')
      }
    }
    else {
      console.log('ℹ️  跳过推送，您可以稍后手动执行: git push')
    }
  }
  catch (error) {
    // 处理用户取消操作
    if (!error || error.message === '' || error.message === 'cancelled' || error.name === 'ExitPromptError') {
      console.log('\n❌ 操作已取消')
      return
    }
    console.error('❌ Git 提交失败:', error.message)
    // 不再重新抛出错误，避免未处理的 Promise 拒绝
    process.exit(1)
  }
}

/**
 * Git commit message verify
 * @param {string} [lang] - Language setting
 * @param {RegExp[]} [ignores] - Ignore patterns
 * @returns {Promise<void>} Promise that resolves if verification passes
 * @throws {Error} If commit message doesn't match Conventional Commits standard
 */
export async function gitCommitVerify(lang = 'en-us', ignores = []) {
  const gitPath = await execCommand('git', ['rev-parse', '--show-toplevel'])
  const gitMsgPath = join(gitPath, '.git', 'COMMIT_EDITMSG')
  const commitMsg = readFileSync(gitMsgPath, 'utf8').trim()

  if (ignores.some(regExp => regExp.test(commitMsg))) {
    return
  }

  const CONVENTIONAL_COMMIT_REGEX = /[a-z]+(?:\(.+\))?!?: .+/i

  if (!CONVENTIONAL_COMMIT_REGEX.test(commitMsg)) {
    throw new Error(locales[lang].gitCommitVerify)
  }
}
