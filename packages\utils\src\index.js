/**
 * @cm/utils - 通用工具库
 *
 * 提供加密、存储管理、深拷贝和树结构处理功能的统一入口
 */

import mitt from 'mitt'

// 导出加密工具
export { default as crypto } from './crypto.js'

// 导出深拷贝工具
export { default as cloneDeep, isDeepEqual, shallowClone } from './deep-clone.js'

// 导出DOM工具
export { copyToClipboard, default as domUtils, loadExternalCSS, serialize } from './dom-utils.js'

// 导出存储管理器
export { default as storage } from './storage.js'

// 默认导出（向后兼容）
export { default } from './storage.js'

// 导出树工具（主要函数）
export {
  appendFieldByUniqueId,
  buildHierarchyTree,
  // 构建器
  buildTreeFromFlat,
  // 兼容性别名
  optimizeTreeStructure as deleteChildren,
  extractFieldValues,
  extractPathList,
  filterTree,
  // 操作器
  findNode,
  getNodeByUniqueId,
  getNodePath,
  getTreeStats,
  handleTree,
  mapTree,
  optimizeTreeStructure,
  traverseBreadthFirst,
  // 遍历器
  traverseDepthFirst,
} from './tree-utils/index.js'

const mitter = mitt()
export { mitter }

// 导出验证工具
export { isEmpty, isNotEmpty, isValidEmail, isValidJSON, isValidPhone, default as validators } from './validators.js'
